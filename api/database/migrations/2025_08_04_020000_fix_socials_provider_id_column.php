<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Check if the table exists and has the old column name
        if (Schema::hasTable('socials') && Schema::hasColumn('socials', 'provider_id')) {
            Schema::table('socials', function (Blueprint $table) {
                // Drop the old unique constraint if it exists
                try {
                    $table->dropUnique(['provider', 'provider_id']);
                } catch (\Exception $e) {
                    // Ignore if constraint doesn't exist
                }
                
                // Rename the column
                $table->renameColumn('provider_id', 'provider_user_id');
            });
            
            // Add the new unique constraint
            Schema::table('socials', function (Blueprint $table) {
                $table->unique(['provider', 'provider_user_id']);
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Check if the table exists and has the new column name
        if (Schema::hasTable('socials') && Schema::hasColumn('socials', 'provider_user_id')) {
            Schema::table('socials', function (Blueprint $table) {
                // Drop the new unique constraint
                try {
                    $table->dropUnique(['provider', 'provider_user_id']);
                } catch (\Exception $e) {
                    // Ignore if constraint doesn't exist
                }
                
                // Rename the column back
                $table->renameColumn('provider_user_id', 'provider_id');
            });
            
            // Add the old unique constraint back
            Schema::table('socials', function (Blueprint $table) {
                $table->unique(['provider', 'provider_id']);
            });
        }
    }
};
