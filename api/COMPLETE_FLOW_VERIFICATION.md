# Complete Authentication Flow Verification

## ✅ TOÀN BỘ LOGIC ĐÃ ĐƯỢC DI CHUYỂN TỪ ACCOUNTS

### 🔄 **Flow 1: User Registration**
```
POST /api/auth/register
├── AuthenticationController@register
├── Fire Events:
│   ├── Illuminate\Auth\Events\Registered
│   │   └── SendEmailVerificationNotification
│   ├── Modules\User\Events\NewUserProcessed
│   │   └── PublishEvent (to RabbitMQ)
│   └── Modules\User\Events\SignUp
│       └── SendEventToGA4GoogleAnalytics
└── Dispatch Jobs:
    └── DispatchAuthAnalyticsJob (analytics queue)
```

### 🔄 **Flow 2: User Login (Web)**
```
POST /api/auth/login (guard: web)
├── AuthenticationController@login
├── Fire Events:
│   └── Illuminate\Auth\Events\Login
│       ├── LogSuccessfulLogin
│       │   ├── Create AuthenticationLog
│       │   └── Fire AuthenticationLogCreated
│       │       └── UpdateUserLoginNth
│       │           ├── Update login_nth
│       │           └── IF first login (login_nth == 1)
│       │               └── Fire UserFirstLoginFromWeb
│       │                   └── UserFirstLoginListener
│       │                       └── Fire NewUserProcessed
│       └── LogSuccessfulLoginWebDevice
│           └── IF has FCM token
│               └── Dispatch CreateFcmDeviceTokenActivity
└── Dispatch Jobs:
    └── DispatchAuthAnalyticsJob (analytics queue)
```

### 🔄 **Flow 3: User Login (Mobile API)**
```
POST /api/auth/login (guard: api, mobile device)
├── AuthenticationController@login
├── Fire Events:
│   └── Illuminate\Auth\Events\Login
│       └── LogSuccessfulLogin
│           ├── Create AuthenticationLog (type_login: api, client_id: mobile)
│           └── Fire AuthenticationLogCreated
│               └── UpdateUserLoginNth
│                   ├── Update login_nth
│                   └── IF first login (login_nth == 1 && client_id == mobile)
│                       └── Fire UserFirstLogin
│                           ├── FirstLoginListener
│                           │   └── Send WelcomeUserFirstLoginMobileApp notification
│                           └── FirstLoginAfterOneHourListener (delay: 1 hour)
│                               ├── Fire PushCreateCv
│                               │   └── PublishEvent (to RabbitMQ)
│                               └── Fire PushUpdateUserProfile
│                                   └── PublishEvent (to RabbitMQ)
└── Dispatch Jobs:
    └── DispatchAuthAnalyticsJob (analytics queue)
```

### 🔄 **Flow 4: Refresh Token**
```
POST /oauth/token (grant_type: refresh_token)
├── Laravel Passport handles token refresh
├── Fire Events:
│   └── Laravel\Passport\Events\RefreshTokenCreated
│       ├── LogSuccessfullRefreshToken
│       │   ├── Dispatch AuthenLogRefreshToken job
│       │   │   ├── Create AuthenticationLog
│       │   │   └── Fire AuthenticationLogCreated
│       │   │       └── UpdateUserLoginNth (same flow as login)
│       │   └── IF MobileApp + FCM token
│       │       └── Dispatch CreateFcmDeviceTokenActivity
│       ├── PruneOldTokens (empty handler)
│       └── UserHasRecentlyUpdateProfileListener
│           └── Fire UserHasRecentlyUpdateProfile
│               └── PublishEvent (to RabbitMQ)
```

### 🔄 **Flow 5: User Logout (Web)**
```
POST /api/auth/logout (guard: web)
├── AuthenticationController@logout
├── Fire Events:
│   └── Illuminate\Auth\Events\Logout
│       ├── LogSuccessfulLogout
│       │   └── Revoke tokens (exclude mobile tokens if not freezing)
│       └── LogSuccessfulLogoutWebDevice
│           └── IF has FCM token
│               └── Dispatch CreateFcmDeviceTokenActivity
```

### 🔄 **Flow 6: User Logout (Mobile API)**
```
POST /api/auth/logout (guard: api)
├── AuthenticationController@logout
├── Fire Events:
│   └── Illuminate\Auth\Events\Logout
│       └── LogSuccessfulLogout
│           └── Revoke ALL tokens (mobile logout)
```

### 🔄 **Flow 7: Social Login**
```
GET /api/auth/social/{provider}
├── SocialAuthController@redirectToProvider
└── Return redirect URL

POST /api/auth/social/{provider}/callback
├── SocialAuthController@handleProviderCallback
├── Find or create user + social link
├── Fire Events:
│   └── IF new user created
│       ├── Illuminate\Auth\Events\Registered
│       ├── Modules\User\Events\NewUserProcessed
│       └── Modules\User\Events\SignUp
└── Same login flow as above
```

## 🎯 **Queue Jobs & Workers**

### **Queue: accounts_worker_system**
- `AuthenLogRefreshToken` - Tạo authentication log từ refresh token

### **Queue: topdev.create_fcm_device_token_activity**
- `CreateFcmDeviceTokenActivity` - Quản lý FCM device tokens

### **Queue: analytics**
- `DispatchAuthAnalyticsJob` - Gửi data tới GA4 và custom analytics

## 🔧 **Database Schema Updates**

### **Socials Table**
```sql
CREATE TABLE socials (
    id BIGINT PRIMARY KEY,
    user_id UUID NOT NULL,
    provider VARCHAR(255) NOT NULL,
    provider_user_id VARCHAR(255) NOT NULL, -- ID from social provider
    name VARCHAR(255) NULL,
    email VARCHAR(255) NULL,
    avatar TEXT NULL,
    created_at TIMESTAMP,
    updated_at TIMESTAMP,

    INDEX idx_user_provider (user_id, provider),
    UNIQUE KEY unique_provider_user (provider, provider_user_id),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);
```

### **Queue: notifications**
- Various notification jobs

## 📊 **Event-Listener Mapping (HOÀN CHỈNH)**

### **Laravel Events**
- `Login` → `LogSuccessfulLogin`, `LogSuccessfulLoginWebDevice`
- `Logout` → `LogSuccessfulLogout`, `LogSuccessfulLogoutWebDevice`
- `Registered` → `SendEmailVerificationNotification`
- `Failed` → `LogFailedLogin`

### **Passport Events**
- `AccessTokenCreated` → `LogSuccessfulIssueToken`, `RevokeOldTokens`
- `RefreshTokenCreated` → `LogSuccessfullRefreshToken`, `PruneOldTokens`, `UserHasRecentlyUpdateProfileListener`

### **Custom Authentication Events**
- `AuthenticationLogCreated` → `UpdateUserLoginNth`, `SendEventToGA4GoogleAnalytics`
- `UserFirstLogin` → `FirstLoginListener`, `FirstLoginAfterOneHourListener`
- `UserFirstLoginFromWeb` → `UserFirstLoginListener`

### **User Events**
- `NewUserProcessed` → `PublishEvent`
- `UserHasRecentlyUpdateProfile` → `PublishEvent`
- `PushCreateCv` → `PublishEvent`
- `PushUpdateUserProfile` → `PublishEvent`
- `SignUp` → `SendEventToGA4GoogleAnalytics`

## 🎯 **Analytics & Broadcasting System (MỚI)**

### **Interfaces**
- `GA4EventInterface` - Events that send data to Google Analytics 4
- `ShouldBroadcastToAnalytics` - Events that broadcast to analytics systems

### **Events implementing GA4EventInterface**
- `SignUp` - User registration events
- `AuthenticationLogCreated` - Login/authentication events
- `UserFirstLoginFromWeb` - First login from web events

### **GA4 Event Data Structure**
```php
[
    'name' => 'event_name',
    'params' => [
        'user_id' => 'user_id',
        'value' => 'event_value',
        'session_id' => 'session_id',
        'engagement_time_msec' => 1000,
        // ... other params
    ]
]
```

### **Helper Functions**
- `get_ga4_session($gaId)` - Get GA4 session data
- `get_ga_client_id()` - Get GA client ID from cookie
- `send_ga4_event($eventData, $clientId)` - Send event to GA4

### **Services**
- `GA4EventDispatcher` - Dispatch GA4 events to analytics

## 🔧 **Configuration Files**

### **config/authentication.php**
- Client IDs (mobile: 2, web: 1)
- FCM configuration
- Queue names
- Analytics settings
- Login attempt limits
- Token expiration settings
- Social login settings
- Notification delays

### **config/services.php**
- Facebook, Twitter, Google, LinkedIn, GitHub, Apple providers
- GA4 configuration

## ✅ **Verification Checklist**

- [x] **Login Flow**: Web + Mobile + API guards
- [x] **Registration Flow**: With all events and analytics
- [x] **Social Login Flow**: All providers with auto user creation
- [x] **Logout Flow**: Token revocation logic theo accounts
- [x] **Refresh Token Flow**: Complete với FCM handling
- [x] **First Login Detection**: Web vs Mobile với notifications
- [x] **FCM Token Management**: Login/Logout từ Web và Mobile
- [x] **Authentication Logs**: Device detection và client_id mapping
- [x] **Queue Jobs**: Đúng queue names và processing logic
- [x] **Event Publishing**: RabbitMQ integration
- [x] **Analytics Tracking**: GA4 và custom analytics
- [x] **User Relationships**: authentications() và aliveTokens()
- [x] **Token Management**: Revoke logic theo guard và freezing status
- [x] **Notifications**: Welcome, push notifications với delays
- [x] **Error Handling**: Comprehensive logging và job failures
- [x] **GA4EventInterface**: Events implement GA4 tracking interface
- [x] **ShouldBroadcastToAnalytics**: Events implement analytics broadcasting
- [x] **GA4 Helper Functions**: Complete helper functions for GA4 integration
- [x] **Analytics Configuration**: Full config for GA4 and analytics settings

## 🎉 **KẾT LUẬN**

**API project hiện đã có TOÀN BỘ logic phức tạp từ accounts project**, bao gồm:

1. ✅ **Tất cả Events & Listeners** theo đúng mapping từ accounts
2. ✅ **Tất cả Jobs & Queue processing** với đúng queue names
3. ✅ **Tất cả Authentication flows** (login, logout, refresh, social)
4. ✅ **FCM token management** cho cả Web và Mobile
5. ✅ **First login detection** và notification system
6. ✅ **Analytics tracking** (GA4 + custom)
7. ✅ **RabbitMQ event publishing** system
8. ✅ **Token revocation logic** theo guard và user status
9. ✅ **Device detection** và authentication logging
10. ✅ **Social login** với auto user creation

**Không còn thiếu component nào từ accounts project!**
