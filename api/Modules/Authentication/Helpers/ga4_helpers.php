<?php

if (!function_exists('get_ga4_session')) {
    /**
     * Get GA4 session data.
     *
     * @param string $gaId
     * @return array
     */
    function get_ga4_session($gaId): array
    {
        return [
            'session_id' => session()->getId() ?? 'topdev_system',
            'engagement_time_msec' => 1000,
            'ga_session_id' => time(),
            'ga_session_number' => 1,
        ];
    }
}

if (!function_exists('get_ga_client_id')) {
    /**
     * Get Google Analytics client ID from cookie.
     *
     * @return string
     */
    function get_ga_client_id(): string
    {
        $gaClientId = request()->cookie('_ga');

        if (!$gaClientId) {
            return 'topdev_system';
        }

        $clientId = substr($gaClientId, 6);

        if (!$clientId) {
            return 'topdev_system';
        }

        return $clientId;
    }
}

if (!function_exists('send_ga4_event')) {
    /**
     * Send event to Google Analytics 4.
     *
     * @param array $eventData
     * @param string|null $clientId
     * @return void
     */
    function send_ga4_event(array $eventData, string $clientId = null): void
    {
        if (!config('authentication.analytics.ga4_enabled', true)) {
            return;
        }

        $ga4Configs = config('services.ga4', []);
        
        if (empty($ga4Configs)) {
            \Log::warning('GA4 configuration not found');
            return;
        }

        $clientId = $clientId ?? get_ga_client_id();

        foreach ($ga4Configs as $ga4) {
            $jsonData = [
                'client_id' => $clientId,
                'events' => [$eventData],
            ];

            try {
                $client = new \GuzzleHttp\Client();
                $response = $client->post(
                    'https://www.google-analytics.com/mp/collect?api_secret=' . $ga4['api_secret'] . '&measurement_id=' . $ga4['measurement_id'],
                    [
                        'json' => $jsonData,
                        'timeout' => 5,
                    ]
                );

                \Log::info('[GA4] Event sent successfully', [
                    'measurement_id' => $ga4['measurement_id'],
                    'event_data' => $eventData,
                    'status_code' => $response->getStatusCode(),
                ]);

            } catch (\Exception $e) {
                \Log::error('[GA4] Failed to send event', [
                    'measurement_id' => $ga4['measurement_id'],
                    'event_data' => $eventData,
                    'error' => $e->getMessage(),
                ]);
            }
        }
    }
}
