<?php

namespace Modules\Authentication\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;
use Jen<PERSON>gers\Agent\Agent;
use Modules\Authentication\Entities\AuthenticationLog;
use Mo<PERSON>les\Authentication\Events\AuthenticationLogCreated;
use Modules\User\Entities\User;

class AuthenLogRefreshToken implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The user instance.
     *
     * @var User
     */
    protected $user;

    /**
     * The IP address.
     *
     * @var string
     */
    protected $ip;

    /**
     * The user agent.
     *
     * @var string
     */
    protected $userAgent;

    /**
     * The login timestamp.
     *
     * @var Carbon
     */
    protected $loginAt;

    /**
     * The client ID.
     *
     * @var int
     */
    protected $clientId;

    /**
     * The FCM device token.
     *
     * @var string|null
     */
    protected $fcmToken;

    /**
     * Create a new job instance.
     *
     * @param User $user
     * @param string $ip
     * @param string $userAgent
     * @param Carbon $loginAt
     * @param int $clientId
     * @param string|null $fcmToken
     * @return void
     */
    public function __construct(User $user, $ip, $userAgent, $loginAt, $clientId, $fcmToken = null)
    {
        $this->user = $user;
        $this->ip = $ip;
        $this->userAgent = $userAgent;
        $this->loginAt = $loginAt;
        $this->clientId = $clientId;
        $this->fcmToken = $fcmToken;
        
        $this->onQueue('accounts_worker_system');
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
            Log::info('AuthenLogRefreshToken processing for user: ' . $this->user->email);

            // Detect device type
            $agent = new Agent();
            $agent->setUserAgent($this->userAgent);

            if ($agent->isDesktop() || $agent->isRobot()) {
                $device = 'web';
            } elseif ($agent->isPhone()) {
                $device = 'mobile';
            } else {
                $device = 'tablet';
            }

            // Determine type_login based on client_id
            $typeLogin = $this->clientId == config('authentication.client_mobile') ? 'api' : $device;

            $authenticationLog = new AuthenticationLog([
                'ip_address' => $this->ip,
                'user_agent' => $this->userAgent,
                'login_at' => $this->loginAt,
                'type_login' => $typeLogin,
                'client_id' => $this->clientId,
            ]);

            $this->user->authentications()->save($authenticationLog);

            Log::info('AuthenticationLog created via refresh token', [
                'user_id' => $this->user->id,
                'log_id' => $authenticationLog->id,
                'type_login' => $typeLogin,
                'client_id' => $this->clientId,
            ]);

            // Fire AuthenticationLogCreated event
            event(new AuthenticationLogCreated($authenticationLog, $this->fcmToken));

        } catch (\Exception $e) {
            Log::error('AuthenLogRefreshToken job failed: ' . $e->getMessage(), [
                'user_id' => $this->user->id,
                'error' => $e->getMessage(),
            ]);
            
            throw $e;
        }
    }

    /**
     * Handle a job failure.
     *
     * @param \Throwable $exception
     * @return void
     */
    public function failed(\Throwable $exception)
    {
        Log::error('AuthenLogRefreshToken job failed', [
            'user_id' => $this->user->id,
            'error' => $exception->getMessage(),
        ]);
    }
}
