<?php

namespace Modules\Authentication\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;
use Jen<PERSON>gers\Agent\Agent;
use Modules\Authentication\Entities\AuthenticationLog;
use Mo<PERSON>les\Authentication\Events\AuthenticationLogCreated;
use Modules\User\Entities\User;

class AuthenLogRefreshToken implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The user instance.
     *
     * @var User
     */
    protected $user;

    /**
     * The IP address.
     *
     * @var string
     */
    protected $ip;

    /**
     * The user agent.
     *
     * @var string
     */
    protected $userAgent;

    /**
     * The login timestamp.
     *
     * @var Carbon
     */
    protected $loginAt;

    /**
     * The client ID.
     *
     * @var int
     */
    protected $clientId;

    /**
     * The FCM device token.
     *
     * @var string|null
     */
    protected $fcmToken;

    /**
     * Create a new job instance.
     *
     * @param User $user
     * @param string $ip
     * @param string $userAgent
     * @param Carbon $loginAt
     * @param int $clientId
     * @param string|null $fcmToken
     * @return void
     */
    public function __construct(User $user, $ip, $userAgent, $loginAt, $clientId, $fcmToken = null)
    {
        $this->user = $user;
        $this->ip = $ip;
        $this->userAgent = $userAgent;
        $this->loginAt = $loginAt;
        $this->clientId = $clientId;
        $this->fcmToken = $fcmToken;
        
        $this->onQueue('accounts_worker_system');
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        // Create authentication log exactly like accounts
        $authenticationLog = new AuthenticationLog([
            'ip_address' => $this->ip,
            'user_agent' => $this->userAgent,
            'login_at'   => $this->loginAt,
            'client_id'  => $this->clientId,
            'type_login' => 'api', // Always 'api' for refresh token like accounts
        ]);

        Log::info('------Event AuthenLogRefreshToken save authenticationLog');
        $this->user->authentications()->save($authenticationLog);

        // Fire AuthenticationLogCreated event with FCM token
        event(new AuthenticationLogCreated($authenticationLog, $this->fcmToken));
    }

    /**
     * Handle a job failure.
     *
     * @param \Throwable $exception
     * @return void
     */
    public function failed(\Throwable $exception)
    {
        Log::error('AuthenLogRefreshToken job failed', [
            'user_id' => $this->user->id,
            'error' => $exception->getMessage(),
        ]);
    }
}
