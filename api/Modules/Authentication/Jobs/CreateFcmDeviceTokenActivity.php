<?php

namespace Modules\Authentication\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class CreateFcmDeviceTokenActivity implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The user ID.
     *
     * @var string
     */
    protected $userId;

    /**
     * The FCM device token data.
     *
     * @var array
     */
    protected $data;

    /**
     * Create a new job instance.
     *
     * @param  string  $userId
     * @param  array  $data
     * @return void
     */
    public function __construct($userId, $data = [])
    {
        $this->userId = $userId;
        $this->data = $data;

        $this->onConnection(config('queue.ams'));
    }

    /**
     * Execute the job.
     *
     * @return void
     * @throws \Exception
     */
    public function handle(): void
    {
        try {
            // Get user from ID
            $userClass = config('auth.providers.users.model');
            $user = $userClass::find($this->userId);

            if (!$user) {
                Log::warning('CreateFcmDeviceTokenActivity: User not found', [
                    'user_id' => $this->userId
                ]);
                return;
            }

            Log::info('CreateFcmDeviceTokenActivity for user: ' . $user->email);

            // Extract FCM token from data (like accounts)
            $fcmToken = $this->data['fcm_device_token'] ?? null;
            $action = $this->data['action'] ?? 'unknown';
            $source = $this->data['source'] ?? 'unknown';

            if (!$fcmToken) {
                Log::warning('CreateFcmDeviceTokenActivity: No FCM token provided', [
                    'user_id' => $this->userId,
                    'data' => $this->data
                ]);
                return;
            }
        } catch (\Exception $e) {
            Log::error('Failed to create FCM device token activity: ' . $e->getMessage(), [
                'user_id' => $this->userId,
                'data' => $this->data,
                'error' => $e->getMessage(),
            ]);

            throw $e;
        }
    }

    /**
     * Handle a job failure.
     *
     * @param  \Throwable  $exception
     * @return void
     */
    public function failed(\Throwable $exception)
    {
        Log::error('CreateFcmDeviceTokenActivity job failed', [
            'user_id' => $this->userId,
            'data' => $this->data,
            'error' => $exception->getMessage(),
        ]);
    }
}
