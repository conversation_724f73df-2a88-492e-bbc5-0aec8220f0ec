<?php

namespace Modules\Authentication\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Modules\User\Entities\User;

class CreateFcmDeviceTokenActivity implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The user instance.
     *
     * @var User
     */
    protected $user;

    /**
     * The FCM device token.
     *
     * @var string
     */
    protected $fcmToken;

    /**
     * Additional data.
     *
     * @var array
     */
    protected $data;

    /**
     * Create a new job instance.
     *
     * @param  User  $user
     * @param  string  $fcmToken
     * @param  array  $data
     * @return void
     */
    public function __construct(User $user, $fcmToken, $data = [])
    {
        $this->user = $user;
        $this->fcmToken = $fcmToken;
        $this->data = $data;
        
        $this->onQueue('topdev.create_fcm_device_token_activity');
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
            Log::info('CreateFcmDeviceTokenActivity for user: ' . $this->user->email);
            
            // Store FCM token for the user
            // This could be stored in a separate table or as user metadata
            $this->user->setMeta('fcm_device_token', $this->fcmToken);
            $this->user->setMeta('fcm_token_updated_at', now());
            
            // Log the activity
            Log::info('FCM device token created/updated for user: ' . $this->user->id, [
                'user_id' => $this->user->id,
                'fcm_token' => substr($this->fcmToken, 0, 20) . '...', // Log partial token for security
                'data' => $this->data,
            ]);
            
        } catch (\Exception $e) {
            Log::error('Failed to create FCM device token activity: ' . $e->getMessage(), [
                'user_id' => $this->user->id,
                'error' => $e->getMessage(),
            ]);
            
            throw $e;
        }
    }

    /**
     * Handle a job failure.
     *
     * @param  \Throwable  $exception
     * @return void
     */
    public function failed(\Throwable $exception)
    {
        Log::error('CreateFcmDeviceTokenActivity job failed', [
            'user_id' => $this->user->id,
            'error' => $exception->getMessage(),
        ]);
    }
}
