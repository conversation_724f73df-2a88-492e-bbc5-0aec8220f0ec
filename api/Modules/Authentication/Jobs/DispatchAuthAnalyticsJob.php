<?php

namespace Modules\Authentication\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Modules\User\Entities\User;

class DispatchAuthAnalyticsJob extends DispatchAnalyticsJob
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The user instance.
     *
     * @var User
     */
    protected $user;

    /**
     * The event type (login, logout, register, etc.).
     *
     * @var string
     */
    protected $eventType;

    /**
     * Additional event data.
     *
     * @var array
     */
    protected $eventData;

    /**
     * Create a new job instance.
     *
     * @param  User  $user
     * @param  string  $eventType
     * @param  array  $eventData
     * @return void
     */
    public function __construct(User $user, $eventType, $eventData = [])
    {
        $this->user = $user;
        $this->eventType = $eventType;
        $this->eventData = $eventData;
        
        $this->onQueue('analytics');
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
            Log::info('Dispatching auth analytics for user: ' . $this->user->email, [
                'event_type' => $this->eventType,
                'user_id' => $this->user->id,
            ]);

            // Send to Google Analytics 4
            $this->sendToGA4();
            
            // Send to other analytics services if needed
            $this->sendToCustomAnalytics();
            
        } catch (\Exception $e) {
            Log::error('Failed to dispatch auth analytics: ' . $e->getMessage(), [
                'user_id' => $this->user->id,
                'event_type' => $this->eventType,
                'error' => $e->getMessage(),
            ]);
            
            throw $e;
        }
    }

    /**
     * Send analytics data to Google Analytics 4.
     *
     * @return void
     */
    protected function sendToGA4()
    {
        $ga4Config = config('services.ga4');
        
        if (empty($ga4Config)) {
            Log::warning('GA4 configuration not found');
            return;
        }

        foreach ($ga4Config as $config) {
            $eventData = [
                'name' => $this->eventType,
                'params' => array_merge([
                    'user_id' => $this->user->id,
                    'user_type' => $this->user->type,
                    'timestamp' => now()->timestamp,
                ], $this->eventData),
            ];

            // Here you would implement the actual GA4 API call
            Log::info('GA4 Event Data', $eventData);
        }
    }

    /**
     * Send analytics data to custom analytics service.
     *
     * @return void
     */
    protected function sendToCustomAnalytics()
    {
        // Implement custom analytics tracking here
        Log::info('Custom Analytics Event', [
            'event_type' => $this->eventType,
            'user_id' => $this->user->id,
            'user_type' => $this->user->type,
            'event_data' => $this->eventData,
            'timestamp' => now()->toISOString(),
        ]);
    }

    /**
     * Handle a job failure.
     *
     * @param  \Throwable  $exception
     * @return void
     */
    public function failed(\Throwable $exception)
    {
        Log::error('DispatchAuthAnalyticsJob failed', [
            'user_id' => $this->user->id,
            'event_type' => $this->eventType,
            'error' => $exception->getMessage(),
        ]);
    }
}
