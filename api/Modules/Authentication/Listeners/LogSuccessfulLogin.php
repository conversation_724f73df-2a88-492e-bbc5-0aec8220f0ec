<?php

namespace Modules\Authentication\Listeners;

use Illuminate\Auth\Events\Login;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;
use Jenssegers\Agent\Agent;
use Modules\Authentication\Entities\AuthenticationLog;
use Modules\Authentication\Events\AuthenticationLogCreated;
use Modules\User\Entities\User;

class LogSuccessfulLogin
{
    /**
     * The request.
     *
     * @var Request
     */
    protected $request;

    /**
     * Create the event listener.
     *
     * @param  Request  $request
     *
     * @return void
     */
    public function __construct(Request $request)
    {
        $this->request = $request;
    }

    /**
     * Handle the event.
     *
     * @param  Login  $event
     * @return void
     */
    public function handle(Login $event): void
    {
        /** @var User $user */
        $user = $event->user;
        $ip = $this->request->ip();
        $userAgent = $this->request->userAgent();

        // Detect device type
        $agent = new Agent();
        $agent->setUserAgent($userAgent);

        if ($agent->isDesktop() || $agent->isRobot()) {
            $device = 'web';
        } elseif ($agent->isPhone()) {
            $device = 'mobile';
        } else {
            $device = 'tablet';
        }

        // Determine client ID based on guard and device
        $clientId = $this->getClientId($event->guard, $device);

        $authenticationLog = new AuthenticationLog([
            'ip_address' => $ip,
            'user_agent' => $userAgent,
            'login_at' => Carbon::now(),
            'type_login' => $device,
            'client_id' => $clientId,
        ]);

        $user->authentications()->save($authenticationLog);

        // Get device token from headers
        $deviceToken = $this->request->header('X-Topdev-Device-Token') ?? null;

        Log::info('LogSuccessfulLogin: User ' . $user->email . ' logged in', [
            'user_id' => $user->id,
            'ip' => $ip,
            'device' => $device,
            'guard' => $event->guard,
            'client_id' => $clientId,
        ]);

        event(new AuthenticationLogCreated($authenticationLog, $deviceToken));
    }

    /**
     * Get client ID based on guard and device type.
     *
     * @param  string  $guard
     * @param string $device
     *
     * @return int
     */
    protected function getClientId(string $guard, $device)
    {
        if ($guard === 'api') {
            return $device === 'mobile'
                ? config('authentication.client_mobile', 2)
                : config('authentication.client_web', 1);
        }

        return config('authentication.client_web', 1);
    }
}
