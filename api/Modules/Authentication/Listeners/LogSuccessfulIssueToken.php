<?php

namespace Modules\Authentication\Listeners;

use <PERSON><PERSON>\Passport\Events\AccessTokenCreated;
use Illuminate\Http\Request;
use Modules\Authentication\Services\AuthenticationService;

class LogSuccessfulIssueToken
{
    /**
     * The authentication service.
     *
     * @var \Modules\Authentication\Services\AuthenticationService
     */
    protected $authService;

    /**
     * The request.
     *
     * @var \Illuminate\Http\Request
     */
    protected $request;

    /**
     * Create the event listener.
     *
     * @param  \Modules\Authentication\Services\AuthenticationService  $authService
     * @param  \Illuminate\Http\Request  $request
     * @return void
     */
    public function __construct(AuthenticationService $authService, Request $request)
    {
        $this->authService = $authService;
        $this->request = $request;
    }

    /**
     * Handle the event.
     *
     * @param  \Laravel\Passport\Events\AccessTokenCreated  $event
     * @return void
     */
    public function handle(AccessTokenCreated $event)
    {
        // Get the user model
        $userModel = config('auth.providers.users.model');
        $user = (new $userModel)->find($event->userId);
        
        if ($user) {
            $ip = $this->request->ip();
            $userAgent = $this->request->userAgent();
            
            // Log the token issuance (treated as a login)
            $log = $this->authService->logLogin($user, $ip, $userAgent, 'api_token', $event->clientId);
            
            // Update the user's last login info
            if ($log && method_exists($user, 'updateLastLogin')) {
                $user->updateLastLogin($log->id);
            }
        }
    }
}
