<?php

namespace Modules\Authentication\Listeners;

use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Laravel\Passport\Events\AccessTokenCreated;
use Modules\Authentication\Entities\AuthenticationLog;
use Modules\Authentication\Events\AuthenticationLogCreated;

class LogSuccessfulIssueToken implements ShouldQueue
{
    protected $request;

    /**
     * The time (seconds) before the job should be processed.
     *
     * @var int
     */
    public $delay = 10;

    /**
     * Create the event listener.
     *
     * @param Request  $request
     * @return void
     */
    public function __construct(Request $request)
    {
        $this->request = $request;
    }

    /**
     * Handle the event.
     *
     * @param  Laravel\Passport\Events\AccessTokenCreated  $accessToken
     * @return void
     */
    public function handle(AccessTokenCreated $accessToken)
    {
        $user = $this->resolverUser($accessToken);
        $ip = $this->request->ip();
        $userAgent = $this->request->userAgent();
        //$known = $user->authentications()->whereIpAddress($ip)->whereUserAgent($userAgent)->first();

        $authenticationLog = new AuthenticationLog([
            'ip_address' => $ip,
            'user_agent' => $userAgent,
            'login_at' => Carbon::now(),
        ]);

        $user->authentications()->save($authenticationLog);

        event(new AuthenticationLogCreated($authenticationLog));
    }

    protected function resolverUser($accessToken)
    {
        $class = config('auth.providers.users.model');

        return $class::find($accessToken->userId);
    }
}
