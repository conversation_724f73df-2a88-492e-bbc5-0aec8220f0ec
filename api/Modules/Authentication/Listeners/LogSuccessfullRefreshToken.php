<?php

namespace Modules\Authentication\Listeners;

use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;
use <PERSON><PERSON>\Passport\Events\RefreshTokenCreated;
use Modules\Authentication\Entities\Token;
use Modules\Authentication\Jobs\AuthenticationLogRefreshToken;
use Modules\Authentication\Jobs\CreateFcmDeviceTokenActivity;

/**
 * Class LogSuccessfullRefreshToken
 *
 * This listener handles the logging of successful refresh token events.
 */
class LogSuccessfullRefreshToken
{
    /**
     * The request instance.
     *
     * @var Request
     */
    protected Request $request;

    /**
     * Create the event listener.
     *
     * @param Request $request
     * @return void
     */
    public function __construct(Request $request)
    {
        $this->request = $request;
    }

    /**
     * Handle the event.
     *
     * @param RefreshTokenCreated $eventRefreshToken
     * @return void
     */
    public function handle(RefreshTokenCreated $eventRefreshToken): void
    {
        $source = $this->request->header('X-Topdev-Source') ?? '';
        $fcmToken = $this->request->header('X-Topdev-Device-Token') ?? '';

        // Get token info from database (like accounts Token::find)
        /** @var Token $userInfo */
        $userInfo = Token::query()->find($eventRefreshToken->accessTokenId);

        if (!$userInfo) {
            Log::warning('Could not find token info for refresh token', [
                'access_token_id' => $eventRefreshToken->accessTokenId
            ]);
            return;
        }

        $user = $this->resolverUser($userInfo->user_id);

        if (!$user) {
            Log::warning('Could not resolve user for refresh token', [
                'user_id' => $userInfo->user_id
            ]);
            return;
        }

        $ip = $this->request->ip();
        $userAgent = $this->request->userAgent();
        $login_at = Carbon::now();
        $clientId = $userInfo->client_id;

        // Dispatch job to create authentication log (exactly like accounts)
        AuthenticationLogRefreshToken::dispatch($user, $ip, $userAgent, $login_at, $clientId, $fcmToken);

        // Handle FCM token for mobile app (exactly like accounts)
        if ($source == 'MobileApp' && !empty($fcmToken)) {
            Log::info('User [' . $user->email . '] ISSUE the access token on login from MobileApp with device token [' . $fcmToken . ']');
            CreateFcmDeviceTokenActivity::dispatch($user->id, [
                'action' => 'login',
                'fcm_device_token' => $fcmToken,
                'source' => 'MobileApp',
            ])->onConnection('redis_ams');
        }
    }


    /**
     * Resolve user (exactly like accounts).
     *
     * @param $user_id
     * @return mixed
     */
    protected function resolverUser($user_id): mixed
    {
        $class = config('auth.providers.users.model');
        return $class::find($user_id);
    }
}
