<?php

namespace Modules\Authentication\Listeners;

use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;
use <PERSON><PERSON>\Passport\Events\RefreshTokenCreated;
use Modules\Authentication\Jobs\AuthenLogRefreshToken;
use Modules\Authentication\Jobs\CreateFcmDeviceTokenActivity;

class LogSuccessfullRefreshToken
{
    protected $request;

    /**
     * Create the event listener.
     *
     * @param Request $request
     * @return void
     */
    public function __construct(Request $request)
    {
        $this->request = $request;
    }

    /**
     * Handle the event.
     *
     * @param RefreshTokenCreated $eventRefreshToken
     * @return void
     */
    public function handle(RefreshTokenCreated $eventRefreshToken)
    {
        $source = $this->request->header('X-Topdev-Source') ?? '';
        $fcmToken = $this->request->header('X-Topdev-Device-Token') ?? '';

        // Get token info from database (like accounts Token::find)
        $userInfo = $this->getTokenInfo($eventRefreshToken->accessTokenId);

        if (!$userInfo) {
            Log::warning('Could not find token info for refresh token', [
                'access_token_id' => $eventRefreshToken->accessTokenId
            ]);
            return;
        }

        $user = $this->resolverUser($userInfo->user_id);

        if (!$user) {
            Log::warning('Could not resolve user for refresh token', [
                'user_id' => $userInfo->user_id
            ]);
            return;
        }

        $ip = $this->request->ip();
        $userAgent = $this->request->userAgent();
        $login_at = Carbon::now();
        $clientId = $userInfo->client_id;

        // Dispatch job to create authentication log (exactly like accounts)
        AuthenLogRefreshToken::dispatch($user, $ip, $userAgent, $login_at, $clientId, $fcmToken);

        // Handle FCM token for mobile app (exactly like accounts)
        if ($source == 'MobileApp' && !empty($fcmToken)) {
            Log::info('User [' . $user->email . '] ISSUE the access token on login from MobileApp with device token [' . $fcmToken . ']');
            CreateFcmDeviceTokenActivity::dispatch($user->id, [
                'action' => 'login',
                'fcm_device_token' => $fcmToken,
                'source' => 'MobileApp',
            ]);
        }
    }

    /**
     * Get token info from database (like accounts Token::find).
     *
     * @param string $accessTokenId
     * @return object|null
     */
    protected function getTokenInfo($accessTokenId)
    {
        try {
            return \DB::table('oauth_access_tokens')
                ->where('id', $accessTokenId)
                ->first();
        } catch (\Exception $e) {
            Log::error('Error getting token info: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Resolve user (exactly like accounts).
     *
     * @param string $user_id
     * @return mixed
     */
    protected function resolverUser($user_id)
    {
        $class = config('auth.providers.users.model');
        return $class::find($user_id);
    }
}
