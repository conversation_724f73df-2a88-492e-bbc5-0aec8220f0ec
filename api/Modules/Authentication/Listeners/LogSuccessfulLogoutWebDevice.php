<?php

namespace Modules\Authentication\Listeners;

use Illuminate\Auth\Events\Logout;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Modules\Authentication\Jobs\CreateFcmDeviceTokenActivity;

class LogSuccessfulLogoutWebDevice
{
    protected $request;

    /**
     * Create the event listener.
     *
     * @param Request $request
     * @return void
     */
    public function __construct(Request $request)
    {
        $this->request = $request;
    }

    /**
     * Handle the event.
     *
     * @param Logout $logout
     * @return void
     */
    public function handle(Logout $logout)
    {
        if ($logout->guard === 'web') {
            Log::info('LogSuccessfulLogoutWebDevice: ' . $logout->user->getKey());
            $user = $logout->user;

            $fcmToken = $this->request->cookie('topdev-device-token-web') ?? null;
            if (!empty($fcmToken)) {
                Log::info('User [' . $user->email . '] logout with fcm token from Web with device token [' . $fcmToken. ']');
                CreateFcmDeviceTokenActivity::dispatch($user, $fcmToken, [
                    'action' => 'logout',
                    'source' => 'Web'
                ]);
            } else {
                Log::info('Empty LogoutWebDevice: ' . $logout->user->getKey());
            }
        }
    }
}
