<?php

namespace Modules\Authentication\Listeners;

use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Facades\Log;
use Modules\Authentication\Events\UserFirstLogin;
use Modules\User\Events\PushCreateCv;
use Modules\User\Events\PushUpdateUserProfile;

class FirstLoginAfterOneHourListener implements ShouldQueue
{
    use Notifiable;

    /**
     * The time (seconds) before the job should be processed.
     *
     * @var int
     */
    public $delay = 3600;

    /**
     * Handle the event.
     *
     * @param  UserFirstLogin  $event
     * @return void
     */
    public function handle(UserFirstLogin $event): void
    {
        $authenticationLog = $event->authenlog;

        if (!empty($authenticationLog->authenticatable)) {
            Log::info('FirstLoginAfterOneHourListener ' . $authenticationLog->authenticatable->email);
            event(new PushCreateCv($authenticationLog->authenticatable, $event->device_token));
            event(new PushUpdateUserProfile($authenticationLog->authenticatable, ['recent_position'], $event->device_token));
        }
    }
}
