<?php

namespace Modules\Authentication\Listeners;

use Illuminate\Auth\Events\Login;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Modules\Authentication\Jobs\CreateFcmDeviceTokenActivity;

class LogSuccessfulLoginWebDevice
{
    protected $request;

    /**
     * Create the event listener.
     *
     * @param Request $request
     * @return void
     */
    public function __construct(Request $request)
    {
        $this->request = $request;
    }

    /**
     * Handle the event.
     *
     * @param Login $login
     * @return void
     */
    public function handle(Login $login)
    {
        if ($login->guard === 'web') {
            Log::info('LogSuccessfulLoginWebDevice: ' . $login->user->getKey());
            $user = $login->user;

            $fcmToken = $this->request->cookie('topdev-device-token-web') ?? null;
            if (!empty($fcmToken)) {
                Log::info('User [' . $user->email . '] login with fcm token from Web with device token [' . $fcmToken. ']');
                CreateFcmDeviceTokenActivity::dispatch($user, $fcmToken, [
                    'action' => 'login',
                    'source' => 'Web'
                ]);
            } else {
                Log::info('Empty LoginWebDevice: ' . $login->user->getKey());
            }
        }
    }
}
