<?php

namespace Modules\Authentication\Listeners;

use Illuminate\Auth\Events\Logout;
use Illuminate\Support\Facades\Log;

class LogSuccessfulLogout
{
    /**
     * Handle the event.
     *
     * @param  \Illuminate\Auth\Events\Logout  $logout
     * @return void
     */
    public function handle(Logout $logout): void
    {
        Log::info('Starting revoke token user: [' . $logout->user->email . '] #' . $logout->user->id);

        $tokens = $logout->user->aliveTokens;
        $tokens = $this->tokensShouldBeRevoking($logout, $tokens);

        foreach ($tokens as $token) {
            $token->revoke();
            Log::info('Token ID has revoked: ' . $token->id);
            if ($token->refreshToken) {
                $token->refreshToken->revoke();
            }
        }
    }

    protected function tokensShouldBeRevoking(Logout $logout, $tokens)
    {
        if ($this->isGuardWeb($logout) && !$this->userIsFreezing($logout)) {
            $tokens = $tokens->whereNotIn(
                'client_id',
                [config('authentication.client_mobile')]
            );
        }

        return $tokens;
    }

    //Nếu user logout bằng guard web thì sẽ không revoke token của mobile
    protected function isGuardWeb(Logout $logout): bool
    {
        return $logout->guard === 'web';
    }

    //Nếu user đang freezing thì xóa hết token lun nha
    protected function userIsFreezing(Logout $logout)
    {
        return $logout->user->isFreezing();
    }
}
