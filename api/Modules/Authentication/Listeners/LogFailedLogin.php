<?php

namespace Modules\Authentication\Listeners;

use Illuminate\Auth\Events\Failed;
use Illuminate\Support\Facades\Request;
use Modules\Authentication\Services\AuthenticationService;

class LogFailedLogin
{
    /**
     * The authentication service.
     *
     * @var \Modules\Authentication\Services\AuthenticationService
     */
    protected AuthenticationService $authService;

    /**
     * Create the event listener.
     *
     * @param  \Modules\Authentication\Services\AuthenticationService  $authService
     * @return void
     */
    public function __construct(AuthenticationService $authService)
    {
        $this->authService = $authService;
    }

    /**
     * Handle the event.
     *
     * @param  \Illuminate\Auth\Events\Failed  $event
     * @return void
     */
    public function handle(Failed $event): void
    {
        $email = $event->credentials['email'] ?? 'unknown';
        $reason = $event->user ? 'invalid_password' : 'user_not_found';

        $this->authService->logFailedLogin(
            $email,
            Request::ip(),
            Request::userAgent(),
            $reason
        );
    }
}
