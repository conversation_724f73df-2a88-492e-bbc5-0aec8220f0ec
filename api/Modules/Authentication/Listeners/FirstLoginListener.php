<?php

namespace Modules\Authentication\Listeners;

use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Facades\Log;
use Modules\Authentication\Events\UserFirstLogin;
use Modules\Authentication\Notifications\WelcomeUserFirstLoginMobileApp;

class FirstLoginListener implements ShouldQueue
{
    use Notifiable;

    /**
     * Handle the event.
     *
     * @param  UserFirstLogin  $event
     * @return void
     */
    public function handle(UserFirstLogin $event)
    {
        $authenlog = $event->authenlog;

        if (!empty($authenlog->authenticatable)) {
            Log::info('FirstLoginListener ' . $authenlog->authenticatable->email);
            $authenlog->authenticatable->notify(new WelcomeUserFirstLoginMobileApp($authenlog, $event->device_token));
        }
    }
}
