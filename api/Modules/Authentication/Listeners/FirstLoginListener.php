<?php

namespace Modules\Authentication\Listeners;

use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Facades\Log;
use Modules\Authentication\Events\UserFirstLogin;
use Modules\Authentication\Notifications\WelcomeUserFirstLoginMobileApp;

class FirstLoginListener implements ShouldQueue
{
    use Notifiable;

    /**
     * Handle the event.
     *
     * @param  UserFirstLogin  $event
     * @return void
     */
    public function handle(UserFirstLogin $event): void
    {
        $authenticationLog = $event->authenlog;

        if (!empty($authenticationLog->authenticatable)) {
            Log::info('FirstLoginListener ' . $authenticationLog->authenticatable->email);
            $authenticationLog->authenticatable->notify(new WelcomeUserFirstLoginMobileApp($authenticationLog, $event->device_token));
        }
    }
}
