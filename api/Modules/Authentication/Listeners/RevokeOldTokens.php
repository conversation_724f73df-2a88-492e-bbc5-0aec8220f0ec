<?php

namespace Modules\Authentication\Listeners;

use <PERSON><PERSON>\Passport\Events\AccessTokenCreated;
use Illuminate\Support\Facades\Log;

class RevokeOldTokens
{
    /**
     * Handle the event.
     *
     * @param AccessTokenCreated $accessToken
     * @return void
     */
    public function handle(AccessTokenCreated $accessToken)
    {
        // This is intentionally empty as per the accounts implementation
        // The logic for revoking old tokens is handled elsewhere
        Log::info('AccessTokenCreated event received', [
            'token_id' => $accessToken->tokenId,
            'user_id' => $accessToken->userId,
            'client_id' => $accessToken->clientId,
        ]);
    }
}
