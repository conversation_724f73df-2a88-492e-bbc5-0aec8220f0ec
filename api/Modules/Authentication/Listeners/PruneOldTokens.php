<?php

namespace Modules\Authentication\Listeners;

use <PERSON><PERSON>\Passport\Events\RefreshTokenCreated;
use Illuminate\Support\Facades\Log;

class PruneOldTokens
{
    /**
     * Handle the event.
     *
     * @param RefreshTokenCreated $refreshToken
     * @return void
     */
    public function handle(RefreshTokenCreated $refreshToken): void
    {
        // This is intentionally empty as per the accounts implementation
        // The logic for pruning old tokens is handled elsewhere
        Log::info('RefreshTokenCreated event received', [
            'refresh_token_id' => $refreshToken->refreshTokenId,
            'access_token_id' => $refreshToken->accessTokenId,
        ]);
    }
}
