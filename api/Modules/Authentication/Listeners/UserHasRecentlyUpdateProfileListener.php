<?php

namespace Modules\Authentication\Listeners;

use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;
use Lara<PERSON>\Passport\Events\RefreshTokenCreated;
use Modules\Authentication\Entities\Token;
use Modules\User\Entities\User;
use Modules\User\Events\UserHasRecentlyUpdateProfile;

class UserHasRecentlyUpdateProfileListener implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Handle the event.
     *
     * @param  RefreshTokenCreated  $event
     *
     * @return void
     * @throws \Exception
     */
    public function handle(RefreshTokenCreated $event): void
    {
        try {
            // Get user from access token
            $user = $this->resolveUser($event->accessTokenId);
            
            if (!$user) {
                Log::warning('Could not resolve user for refresh token in UserHasRecentlyUpdateProfileListener', [
                    'access_token_id' => $event->accessTokenId
                ]);
                return;
            }

            Log::info('UserHasRecentlyUpdateProfileListener processing for user: ' . $user->email);
            
            // Fire UserHasRecentlyUpdateProfile event
            event(new UserHasRecentlyUpdateProfile($user));
            
        } catch (\Exception $e) {
            Log::error('UserHasRecentlyUpdateProfileListener failed: ' . $e->getMessage(), [
                'access_token_id' => $event->accessTokenId ?? null,
                'error' => $e->getMessage(),
            ]);
            
            throw $e;
        }
    }

    /**
     * Resolve user from access token ID.
     *
     * @param string $accessTokenId
     * @return User|null
     */
    protected function resolveUser($accessTokenId): ?User
    {
        try {
            /** @var Token $token */
            $token = Token::query()->find($accessTokenId);

            if ($token) {
                return User::query()->find($token->user_id);
            }

            return null;
        } catch (\Exception $e) {
            Log::error('Error resolving user from access token: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * Handle a job failure.
     *
     * @param \Throwable $exception
     * @return void
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('UserHasRecentlyUpdateProfileListener job failed', [
            'error' => $exception->getMessage(),
        ]);
    }
}
