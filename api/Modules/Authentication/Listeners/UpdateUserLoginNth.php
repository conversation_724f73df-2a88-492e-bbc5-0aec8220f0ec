<?php

namespace Modules\Authentication\Listeners;

use Illuminate\Contracts\Queue\ShouldQueue;
use Modules\Authentication\Entities\AuthenticationLog;
use Modules\Authentication\Events\AuthenticationLogCreated;
use Modules\Authentication\Events\UserFirstLogin;
use Modules\Authentication\Services\AuthenticationService;

class UpdateUserLoginNth implements ShouldQueue
{
    /**
     * The authentication service.
     *
     * @var AuthenticationService
     */
    protected $authService;

    /**
     * Create the event listener.
     *
     * @param  AuthenticationService  $authService
     *
     * @return void
     */
    public function __construct(AuthenticationService $authService)
    {
        $this->authService = $authService;
    }

    /**
     * Handle the event.
     *
     * @param  AuthenticationLogCreated  $event
     *
     * @return void
     */
    public function handle(AuthenticationLogCreated $event): void
    {
        $log = $event->authenlog;

        // Count total logins for this user using service
        $logsCount = AuthenticationLog::query()->where([
            'client_id' => $log->client_id,
            'type_login' => $log->type_login,
            'authenticatable_id' => $log->authenticatable_id,
        ])->whereNotNull('login_at')->count();

        \Log::info('------Listeners UpdateUserLoginNth update authenticationLog');
        $log->update(['login_nth' => $logsCount]);

        // Check if first login
        if ($logsCount == 1 && $log->type_login == 'web') {
            event(new \Modules\Authentication\Events\UserFirstLoginFromWeb($log));
        } elseif ($logsCount == 1 && $log->type_login == 'api') {
            if ($log->client_id == config('authentication.client_mobile')) {
                event(new UserFirstLogin($log, $event->device_token ?? null));
            }
        }
    }
}
