<?php

namespace Modules\Authentication\Listeners;

use Modules\Authentication\Events\UserFirstLogin;
use Modules\Authentication\Repositories\Contracts\AuthenticationLogRepositoryInterface;

class UpdateUserLoginNth
{
    /**
     * The authentication log repository.
     *
     * @var \Modules\Authentication\Repositories\Contracts\AuthenticationLogRepositoryInterface
     */
    protected $authLogRepository;

    /**
     * Create the event listener.
     *
     * @param  \Modules\Authentication\Repositories\Contracts\AuthenticationLogRepositoryInterface  $authLogRepository
     * @return void
     */
    public function __construct(AuthenticationLogRepositoryInterface $authLogRepository)
    {
        $this->authLogRepository = $authLogRepository;
    }

    /**
     * Handle the event.
     *
     * @param  \Modules\Authentication\Events\AuthenticationLogCreated  $event
     * @return void
     */
    public function handle($event)
    {
        $log = $event->authenlog;

        // Count total logins for this user
        $logsCount = $this->authLogRepository->countUserLogins(
            $log->authenticatable_id,
            $log->authenticatable_type
        );

        \Log::info('------Listeners UpdateUserLoginNth update authenticationLog');
        $log->update(['login_nth' => $logsCount]);

        // Check if first login
        if ($logsCount == 1 && $log->type_login == 'web') {
            event(new \Modules\Authentication\Events\UserFirstLoginFromWeb($log));
        } elseif ($logsCount == 1 && $log->type_login == 'api') {
            if ($log->client_id == config('authentication.client_mobile')) {
                event(new UserFirstLogin($log, $event->device_token ?? null));
            }
        }
    }
}
