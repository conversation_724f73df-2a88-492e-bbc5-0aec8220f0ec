<?php

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for the authentication module.
| These routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group.
|
*/

// Public routes
Route::prefix('auth')->group(function () {
    // Basic authentication
    Route::post('/login', 'AuthenticationController@login')
        ->name('api.auth.login');

    Route::post('/register', 'AuthenticationController@register')
        ->name('api.auth.register');

    // Social authentication
    Route::get('/social/{provider}', 'SocialAuthController@redirectToProvider')
        ->name('api.auth.social.redirect');

    Route::post('/social/{provider}/callback', 'SocialAuthController@handleProviderCallback')
        ->name('api.auth.social.callback');

    // Send verification code
    Route::post('/verification/send', 'VerificationCodeController@send')
        ->name('api.auth.verification.send');

    // Verify code
    Route::post('/verification/verify', 'VerificationCodeController@verify')
        ->name('api.auth.verification.verify');
});

// Protected routes (require authentication)
Route::middleware('auth:api')->group(function () {
    Route::prefix('auth')->group(function () {
        // User profile
        Route::get('/me', 'AuthenticationController@me')
            ->name('api.auth.me');

        Route::post('/logout', 'AuthenticationController@logout')
            ->name('api.auth.logout');

        // Social account management
        Route::get('/social/accounts', 'SocialAuthController@getLinkedAccounts')
            ->name('api.auth.social.accounts');

        Route::post('/social/{provider}/link', 'SocialAuthController@linkAccount')
            ->name('api.auth.social.link');

        Route::delete('/social/{provider}/unlink', 'SocialAuthController@unlinkAccount')
            ->name('api.auth.social.unlink');

        // Authentication logs
        Route::get('/logs', 'AuthenticationLogController@index')
            ->name('api.auth.logs.index');

        Route::get('/logs/{id}', 'AuthenticationLogController@show')
            ->name('api.auth.logs.show');
    });
});
