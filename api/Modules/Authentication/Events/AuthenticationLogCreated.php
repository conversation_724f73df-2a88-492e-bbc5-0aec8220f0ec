<?php

namespace Modules\Authentication\Events;

use Illuminate\Queue\SerializesModels;
use Modules\Authentication\Contracts\GA4EventInterface;
use Modules\Authentication\Contracts\ShouldBroadcastToAnalytics;
use Modules\Authentication\Entities\AuthenticationLog;

final class AuthenticationLogCreated implements ShouldBroadcastToAnalytics, GA4EventInterface
{
    use SerializesModels;

    /**
     * @var AuthenticationLog
     */
    public $authenlog;

    /**
     * The device token for FCM notifications.
     *
     * @var string|null
     */
    public $device_token;

    protected $eventCategory = 'Job';
    protected $eventAction = 'login';

    /**
     * Create a new Event update model instance.
     *
     * @param AuthenticationLog $authenticationLog
     * @param string|null $device_token
     */
    public function __construct(AuthenticationLog $authenticationLog, $device_token = null)
    {
        $this->authenlog = $authenticationLog;
        $this->device_token = $device_token;
    }

    public function withAnalytics($analytics)
    {
        if (method_exists($analytics, 'setEventValue')) {
            $analytics
                ->setEventValue($this->authenlog->authenticatable_id)
                ->setEventCategory($this->eventCategory)
                ->setEventAction($this->eventAction);
        }
    }

    public function getGa4EventData($gaId): array
    {
        return [
            'name' => $this->eventAction,
            'params' => array_merge([
                'user_id' => $this->authenlog->authenticatable_id,
                'value' => $this->authenlog->authenticatable_id,
                'login_type' => $this->authenlog->type_login,
                'client_id' => $this->authenlog->client_id,
            ], get_ga4_session($gaId)),
        ];
    }
}
