<?php

namespace Modules\Authentication\Events;

use Illuminate\Queue\SerializesModels;
use Modules\Authentication\Contracts\GA4EventInterface;
use Modules\Authentication\Contracts\ShouldBroadcastToAnalytics;
use Modules\User\Entities\User;

final class SignUp implements ShouldBroadcastToAnalytics, GA4EventInterface
{
    use SerializesModels;

    /**
     * @var User
     */
    public $user;

    /**
     * Additional signup data.
     *
     * @var array
     */
    public $data;

    protected $eventCategory = 'Auth';
    protected $eventAction = 'sign_up_server';

    /**
     * Create a new SignUp Event.
     *
     * @param User $user
     * @param array $data
     */
    public function __construct(User $user, $data = [])
    {
        $this->user = $user;
        $this->data = $data;
    }

    public function withAnalytics($analytics): void
    {
        if (method_exists($analytics, 'setEventValue')) {
            $analytics
                ->setEventValue($this->user->id)
                ->setEventCategory($this->eventCategory)
                ->setEventAction($this->eventAction);
        }
    }

    public function getGa4EventData($gaId): array
    {
        return [
            'name' => $this->eventAction,
            'params' => array_merge([
                'user_id' => $this->user->id,
                'value' => $this->user->id,
                'user_type' => $this->user->type,
                'method' => $this->data['method'] ?? 'email',
            ], get_ga4_session($gaId)),
        ];
    }

    /**
     * Get user model instance.
     *
     * @return User
     */
    public function user(): User
    {
        return $this->user;
    }
}
