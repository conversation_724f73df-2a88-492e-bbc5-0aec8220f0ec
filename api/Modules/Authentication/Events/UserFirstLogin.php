<?php

namespace Modules\Authentication\Events;

use Illuminate\Queue\SerializesModels;
use Illuminate\Foundation\Events\Dispatchable;
use Modules\Authentication\Entities\AuthenticationLog;

class UserFirstLogin
{
    use Dispatchable, SerializesModels;

    /**
     * The authentication log instance.
     *
     * @var AuthenticationLog
     */
    public $authenlog;

    /**
     * The device token for FCM notifications.
     *
     * @var string|null
     */
    public $device_token;

    /**
     * Create a new event instance.
     *
     * @param  AuthenticationLog  $authenlog
     * @param  string|null  $device_token
     * @return void
     */
    public function __construct(AuthenticationLog $authenlog, $device_token = null)
    {
        $this->authenlog = $authenlog;
        $this->device_token = $device_token;
    }
}
