<?php

namespace Modules\Authentication\Events;

use Illuminate\Queue\SerializesModels;
use Modules\Authentication\Contracts\ShouldBroadcastToAnalytics;
use Modules\Authentication\Entities\AuthenticationLog;

final class UserFirstLoginFromWeb implements ShouldBroadcastToAnalytics
{
    use SerializesModels;

    /**
     * @var AuthenticationLog
     */
    public $authenlog;

    protected $eventCategory = 'Job';
    protected $eventAction = 'first_login';

    /**
     * Create a new Event update model instance.
     *
     * @param AuthenticationLog $authenticationLog
     */
    public function __construct(AuthenticationLog $authenticationLog)
    {
        $this->authenlog = $authenticationLog;
    }

    public function withAnalytics($analytics): void
    {
        if (method_exists($analytics, 'setEventValue')) {
            $analytics
                ->setEventValue($this->authenlog->authenticatable_id)
                ->setEventCategory($this->eventCategory)
                ->setEventAction($this->eventAction);
        }
    }
}
