<?php

namespace Modules\Authentication\Events;

use App\Contracts\ShouldBroadcastToAnalytics;
use Illuminate\Queue\SerializesModels;
use Modules\Authentication\Entities\AuthenticationLog;

final class UserFirstLoginFromWeb implements ShouldBroadcastToAnalytics
{
    use SerializesModels;

    /**
     * @var AuthenticationLog
     */
    public $authenlog;

    protected $eventCategory = 'Job';
    protected $eventAction = 'first_login';

    /**
     * Create a new Event update model instance.
     *
     * @param AuthenticationLog $authenlog
     */
    public function __construct(AuthenticationLog $authenlog)
    {
        $this->authenlog = $authenlog;
    }

    public function withAnalytics($analytics)
    {
        if (method_exists($analytics, 'setEventValue')) {
            $analytics
                ->setEventValue($this->authenlog->authenticatable_id)
                ->setEventCategory($this->eventCategory)
                ->setEventAction($this->eventAction);
        }
    }
}
