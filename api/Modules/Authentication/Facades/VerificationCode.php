<?php

namespace Modules\Authentication\Facades;

use Illuminate\Support\Facades\Facade;

/**
 * @method static \Modules\Authentication\Entities\VerificationCode generateAndSend(string $email, string $type = 'email_verification')
 * @method static bool verify(string $email, string $code, string $type = 'email_verification')
 * @method static int cleanExpiredCodes()
 *
 * @see \Modules\Authentication\Services\VerificationCodeService
 */
class VerificationCode extends Facade
{
    /**
     * Get the registered name of the component.
     *
     * @return string
     */
    protected static function getFacadeAccessor(): string
    {
        return 'verification_code';
    }
}
