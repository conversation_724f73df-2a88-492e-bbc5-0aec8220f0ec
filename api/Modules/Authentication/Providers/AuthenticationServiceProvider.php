<?php

namespace Modules\Authentication\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Database\Eloquent\Factory;
use Laravel\Passport\Passport;
use Modules\Authentication\Support\CodeGenerator;

class AuthenticationServiceProvider extends ServiceProvider
{
    /**
     * Boot the application events.
     *
     * @return void
     */
    public function boot(): void
    {
        $this->registerTranslations();
        $this->registerConfig();
        $this->registerViews();
        $this->registerFactories();
        $this->loadMigrationsFrom(module_path('Authentication', 'Database/Migrations'));
    }

    /**
     * Register the service provider.
     *
     * @return void
     */
    public function register(): void
    {
        // Register route service provider
        $this->app->register(RouteServiceProvider::class);
        
        // Register event service provider
        $this->app->register(EventServiceProvider::class);
        
        // Bind CodeGenerator
        $this->app->singleton(CodeGenerator::class, function () {
            return new CodeGenerator();
        });
        
        // Configure Passport models
        $this->configurePassport();

        // Register the verification code service for facade
        $this->app->singleton('verification_code', function ($app) {
            return $app->make(\Modules\Authentication\Services\VerificationCodeService::class);
        });

        // Register commands
        $this->registerCommands();

        // Register middleware
        $this->registerMiddleware();
    }
    
    /**
     * Register the console commands.
     *
     * @return void
     */
    protected function registerCommands(): void
    {
        if ($this->app->runningInConsole()) {
            $this->commands([
                \Modules\Authentication\Commands\AuthenticationLogWithLoginNthSyncCommand::class,
                \Modules\Authentication\Commands\ClearCommand::class,
                \Modules\Authentication\Commands\ImportElasticCommand::class,
                \Modules\Authentication\Commands\ImportElasticCommand::class,
                \Modules\Authentication\Commands\PullTaxonomyFromOriginCommand::class,
                \Modules\Authentication\Commands\UpdateTypeLoginCommand::class,
            ]);
        }
    }
    
    /**
     * Register the middleware.
     *
     * @return void
     */
    protected function registerMiddleware(): void
    {
        $router = $this->app['router'];

//        $router->aliasMiddleware('auth.api', \Modules\Authentication\Http\Middleware\AuthenticateApi::class);
        $router->aliasMiddleware('auth.employer', \Modules\Authentication\Http\Middleware\EnsureEmployerAuthenticated::class);
    }
    
    /**
     * Configure Passport models and settings
     *
     * @return void
     */
    protected function configurePassport(): void
    {
        // Use custom Passport models
        Passport::useClientModel(\Modules\Authentication\Entities\Client::class);
        Passport::useTokenModel(\Modules\Authentication\Entities\Token::class);
        Passport::useAuthCodeModel(\Modules\Authentication\Entities\AuthCode::class);
        Passport::usePersonalAccessClientModel(\Modules\Authentication\Entities\PersonalAccessClient::class);

        // Configure token expiration
        Passport::tokensExpireIn(now()->addHours(24)); // 24 hours
        Passport::refreshTokensExpireIn(now()->addHours(8760)); // 1 year
        Passport::personalAccessTokensExpireIn(now()->addMonths(6)); // 6 months
    }

    /**
     * Register config.
     *
     * @return void
     */
    protected function registerConfig(): void
    {
        $this->publishes([
            module_path('Authentication', 'Config/config.php') => config_path('authentication.php'),
        ], 'config');
        $this->mergeConfigFrom(
            module_path('Authentication', 'Config/config.php'), 'authentication'
        );
    }

    /**
     * Register views.
     *
     * @return void
     */
    public function registerViews(): void
    {
        $viewPath = resource_path('views/modules/authentication');

        $sourcePath = module_path('Authentication', 'Resources/views');

        $this->publishes([
            $sourcePath => $viewPath
        ],'views');

        $this->loadViewsFrom(array_merge(array_map(function ($path) {
            return $path . '/modules/authentication';
        }, \Config::get('view.paths')), [$sourcePath]), 'authentication');
    }

    /**
     * Register translations.
     *
     * @return void
     */
    public function registerTranslations(): void
    {
        $langPath = resource_path('lang/modules/authentication');

        if (is_dir($langPath)) {
            $this->loadTranslationsFrom($langPath, 'authentication');
        } else {
            $this->loadTranslationsFrom(module_path('Authentication', 'Resources/lang'), 'authentication');
        }
    }

    /**
     * Register an additional directory of factories.
     *
     * @return void
     */
    public function registerFactories(): void
    {
        if (! app()->environment('production') && $this->app->runningInConsole()) {
            app(Factory::class)->load(module_path('Authentication', 'Database/factories'));
        }
    }

    /**
     * Get the services provided by the provider.
     *
     * @return array
     */
    public function provides(): array
    {
        return [];
    }
}
