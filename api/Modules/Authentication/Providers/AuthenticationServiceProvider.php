<?php

namespace Modules\Authentication\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Database\Eloquent\Factory;
use Modules\Authentication\Support\CodeGenerator;

class AuthenticationServiceProvider extends ServiceProvider
{
    /**
     * Boot the application events.
     *
     * @return void
     */
    public function boot(): void
    {
        $this->registerTranslations();
        $this->registerConfig();
        $this->registerViews();
        $this->registerFactories();
        $this->loadMigrationsFrom(module_path('Authentication', 'Database/Migrations'));
    }

    /**
     * Register the service provider.
     *
     * @return void
     */
    public function register(): void
    {
        // Register route service provider
        $this->app->register(RouteServiceProvider::class);
        
        // Register event service provider
        $this->app->register(EventServiceProvider::class);
        
        // Bind CodeGenerator
        $this->app->singleton(CodeGenerator::class, function () {
            return new CodeGenerator();
        });
        
        // Register the verification code service for facade
        $this->app->singleton('verification_code', function ($app) {
            return $app->make(\Modules\Authentication\Services\VerificationCodeService::class);
        });

        // Register services
        $this->registerServices();

        // Register commands
        $this->registerCommands();

        // Register middleware
        $this->registerMiddleware();
    }
    
    /**
     * Register the console commands.
     *
     * @return void
     */
    protected function registerCommands(): void
    {
        if ($this->app->runningInConsole()) {
            $this->commands([
                \Modules\Authentication\Commands\AuthenticationLogWithLoginNthSyncCommand::class,
                \Modules\Authentication\Commands\ClearCommand::class,
                \Modules\Authentication\Commands\ImportElasticCommand::class,
                \Modules\Authentication\Commands\ImportElasticCommand::class,
                \Modules\Authentication\Commands\PullTaxonomyFromOriginCommand::class,
                \Modules\Authentication\Commands\UpdateTypeLoginCommand::class,
            ]);
        }
    }
    
    /**
     * Register the middleware.
     *
     * @return void
     */
    protected function registerMiddleware(): void
    {
        $router = $this->app['router'];
        $router->aliasMiddleware('verified', \Modules\Authentication\Http\Middleware\EnsureEmailIsVerified::class);
    }
    
    /**
     * Register services
     *
     * @return void
     */
    protected function registerServices(): void
    {
        // Register Authentication Service
        $this->app->singleton(\Modules\Authentication\Services\AuthenticationService::class);

        // Register Verification Code Service
        $this->app->singleton(\Modules\Authentication\Services\VerificationCodeService::class);
    }

    /**
     * Register config.
     *
     * @return void
     */
    protected function registerConfig(): void
    {
        $this->publishes([
            module_path('Authentication', 'Config/config.php') => config_path('authentication.php'),
        ], 'config');
        $this->mergeConfigFrom(
            module_path('Authentication', 'Config/config.php'), 'authentication'
        );
    }

    /**
     * Register views.
     *
     * @return void
     */
    public function registerViews(): void
    {
        $viewPath = resource_path('views/modules/authentication');

        $sourcePath = module_path('Authentication', 'Resources/views');

        $this->publishes([
            $sourcePath => $viewPath
        ],'views');

        $this->loadViewsFrom(array_merge(array_map(function ($path) {
            return $path . '/modules/authentication';
        }, \Config::get('view.paths')), [$sourcePath]), 'authentication');
    }

    /**
     * Register translations.
     *
     * @return void
     */
    public function registerTranslations(): void
    {
        $langPath = resource_path('lang/modules/authentication');

        if (is_dir($langPath)) {
            $this->loadTranslationsFrom($langPath, 'authentication');
        } else {
            $this->loadTranslationsFrom(module_path('Authentication', 'Resources/lang'), 'authentication');
        }
    }

    /**
     * Register an additional directory of factories.
     *
     * @return void
     */
    public function registerFactories(): void
    {
        if (! app()->environment('production') && $this->app->runningInConsole()) {
            app(Factory::class)->load(module_path('Authentication', 'Database/factories'));
        }
    }

    /**
     * Get the services provided by the provider.
     *
     * @return array
     */
    public function provides(): array
    {
        return [];
    }
}
