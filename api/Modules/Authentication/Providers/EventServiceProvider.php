<?php

namespace Modules\Authentication\Providers;

use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;
use Modules\Authentication\Events\UserFirstLogin;
use Modules\Authentication\Events\UserFirstLoginFromWeb;
use Modules\Authentication\Events\VerifySuccessfully;
use Modules\Authentication\Listeners\FirstLoginAfterOneHourListener;
use Modules\Authentication\Listeners\FirstLoginListener;
use Modules\Authentication\Listeners\LogSuccessfulIssueToken;
use Modules\Authentication\Listeners\LogSuccessfulLogin;
use Modules\Authentication\Listeners\LogSuccessfulLogout;
use Modules\Authentication\Listeners\LogSuccessfullRefreshToken;
use Modules\Authentication\Listeners\UpdateUserLoginNth;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event listener mappings for the application.
     *
     * @var array
     */
    protected $listen = [
        // Laravel authentication events
        \Illuminate\Auth\Events\Login::class => [
            \Modules\Authentication\Listeners\LogSuccessfulLogin::class,
            \Modules\Authentication\Listeners\LogSuccessfulLoginWebDevice::class,
        ],
        \Illuminate\Auth\Events\Logout::class => [
            \Modules\Authentication\Listeners\LogSuccessfulLogout::class,
            \Modules\Authentication\Listeners\LogSuccessfulLogoutWebDevice::class,
            \Modules\Authentication\Listeners\LogSuccessfulLogoutAuthLog::class,
        ],
        \Illuminate\Auth\Events\Failed::class => [
            \Modules\Authentication\Listeners\LogFailedLogin::class,
        ],
        \Illuminate\Auth\Events\Registered::class => [
            \Illuminate\Auth\Listeners\SendEmailVerificationNotification::class,
        ],

        // Custom authentication events
        \Modules\Authentication\Events\AuthenticationLogCreated::class => [
            \Modules\Authentication\Listeners\UpdateUserLoginNth::class,
            \Modules\User\Listeners\SendEventToGA4GoogleAnalytics::class,
        ],
        \Modules\Authentication\Events\UserFirstLogin::class => [
            \Modules\Authentication\Listeners\FirstLoginListener::class,
            \Modules\Authentication\Listeners\FirstLoginAfterOneHourListener::class,
        ],
        \Modules\Authentication\Events\UserFirstLoginFromWeb::class => [
            \Modules\User\Listeners\UserFirstLoginListener::class,
        ],

        // Passport events
        \Laravel\Passport\Events\AccessTokenCreated::class => [
            \Modules\Authentication\Listeners\LogSuccessfulIssueToken::class,
            \Modules\Authentication\Listeners\RevokeOldTokens::class,
        ],
        \Laravel\Passport\Events\RefreshTokenCreated::class => [
            \Modules\Authentication\Listeners\LogSuccessfullRefreshToken::class,
            \Modules\Authentication\Listeners\PruneOldTokens::class,
        ],

        // Verification events
        \Modules\Authentication\Events\VerifySuccessfully::class => [
            // Add any listeners for successful verification
        ],
    ];
    
    /**
     * The subscriber classes to register.
     *
     * @var array
     */
    protected $subscribe = [
        // Add any subscriber classes here
    ];

    /**
     * Register any events for your application.
     *
     * @return void
     */
    public function boot()
    {
        parent::boot();
    }
}
