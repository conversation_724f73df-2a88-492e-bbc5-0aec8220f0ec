<?php

namespace Modules\Authentication\Providers;

use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event listener mappings for the application.
     *
     * @var array
     */
    protected $listen = [
        // Laravel authentication events
        \Illuminate\Auth\Events\Login::class => [
            \Modules\Authentication\Listeners\LogSuccessfulLogin::class,
            \Modules\Authentication\Listeners\LogSuccessfulLoginWebDevice::class,
        ],
        \Illuminate\Auth\Events\Logout::class => [
            \Modules\Authentication\Listeners\LogSuccessfulLogout::class,
            \Modules\Authentication\Listeners\LogSuccessfulLogoutWebDevice::class,
            \Modules\Authentication\Listeners\LogSuccessfulLogoutAuthLog::class,
        ],
        \Illuminate\Auth\Events\Failed::class => [
            \Modules\Authentication\Listeners\LogFailedLogin::class,
        ],
        \Illuminate\Auth\Events\Registered::class => [
            \Illuminate\Auth\Listeners\SendEmailVerificationNotification::class,
        ],

        // Custom authentication events
        \Modules\Authentication\Events\AuthenticationLogCreated::class => [
            \Modules\Authentication\Listeners\UpdateUserLoginNth::class,
            \Modules\Authentication\Listeners\SendEventToGA4GoogleAnalytics::class,
        ],
        \Modules\Authentication\Events\UserFirstLogin::class => [
            \Modules\Authentication\Listeners\FirstLoginListener::class,
            \Modules\Authentication\Listeners\FirstLoginAfterOneHourListener::class,
        ],

        // Passport events
        \Laravel\Passport\Events\AccessTokenCreated::class => [
            \Modules\Authentication\Listeners\LogSuccessfulIssueToken::class,
            \Modules\Authentication\Listeners\RevokeOldTokens::class,
        ],
        \Laravel\Passport\Events\RefreshTokenCreated::class => [
            \Modules\Authentication\Listeners\LogSuccessfullRefreshToken::class,
            \Modules\Authentication\Listeners\PruneOldTokens::class,
            \Modules\Authentication\Listeners\UserHasRecentlyUpdateProfileListener::class,
        ],

        // Verification events
        \Modules\Authentication\Events\VerifySuccessfully::class => [
            // Add any listeners for successful verification
        ],

        // User events
        \Modules\User\Events\NewUserProcessed::class => [
            \Modules\User\Listeners\PublishEvent::class,
        ],
        \Modules\User\Events\UserHasRecentlyUpdateProfile::class => [
            \Modules\User\Listeners\PublishEvent::class,
        ],
        \Modules\Authentication\Events\PushCreateCv::class => [
            \Modules\User\Listeners\PublishEvent::class,
        ],
        \Modules\Authentication\Events\PushUpdateUserProfile::class => [
            \Modules\User\Listeners\PublishEvent::class,
        ],

        // Authentication events from other modules
        \Modules\Authentication\Events\UserFirstLoginFromWeb::class => [
            \Modules\Authentication\Listeners\UserFirstLoginListener::class,
        ],

        // GA4 Events
        \Modules\Authentication\Events\SignUp::class => [
            \Modules\Authentication\Listeners\SendEventToGA4GoogleAnalytics::class,
        ],
    ];
    
    /**
     * The subscriber classes to register.
     *
     * @var array
     */
    protected $subscribe = [
        // Add any subscriber classes here
    ];

    /**
     * Register any events for your application.
     *
     * @return void
     */
    public function boot(): void
    {
        parent::boot();
    }
}
