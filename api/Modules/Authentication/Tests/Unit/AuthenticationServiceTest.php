<?php

namespace Modules\Authentication\Tests\Unit;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Event;
use Modules\Authentication\Services\AuthenticationService;
use Modules\Authentication\Events\AuthenticationLogCreated;
use Mo<PERSON>les\User\Entities\User;

class AuthenticationServiceTest extends TestCase
{
    use RefreshDatabase;

    protected $authService;

    protected function setUp(): void
    {
        parent::setUp();
        $this->authService = app(AuthenticationService::class);
    }

    /** @test */
    public function can_log_successful_login()
    {
        Event::fake();

        $user = User::create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'display_name' => 'Test User',
            'type' => 'resume',
            'email_verified_at' => now()
        ]);

        $log = $this->authService->logLogin(
            $user,
            '127.0.0.1',
            'Mozilla/5.0 Test Browser',
            'web',
            1
        );

        $this->assertDatabaseHas('authentication_logs', [
            'authenticatable_id' => $user->id,
            'ip_address' => '127.0.0.1',
            'type_login' => 'web',
            'client_id' => 1,
        ]);

        Event::assertDispatched(AuthenticationLogCreated::class);
    }

    /** @test */
    public function auto_detects_device_type_from_user_agent()
    {
        $user = User::create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'display_name' => 'Test User',
            'type' => 'resume',
            'email_verified_at' => now()
        ]);

        // Test mobile detection
        $log = $this->authService->logLogin(
            $user,
            '127.0.0.1',
            'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X)',
            'auto'
        );

        $this->assertEquals('mobile', $log->type_login);

        // Test desktop detection
        $log = $this->authService->logLogin(
            $user,
            '127.0.0.1',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'auto'
        );

        $this->assertEquals('web', $log->type_login);
    }

    /** @test */
    public function overrides_type_login_for_mobile_client()
    {
        $user = User::create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'display_name' => 'Test User',
            'type' => 'resume',
            'email_verified_at' => now()
        ]);

        $mobileClientId = config('authentication.client_mobile');

        $log = $this->authService->logLogin(
            $user,
            '127.0.0.1',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'web',
            $mobileClientId
        );

        $this->assertEquals('api', $log->type_login);
    }

    /** @test */
    public function can_get_user_authentication_logs()
    {
        $user = User::create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'display_name' => 'Test User',
            'type' => 'resume',
            'email_verified_at' => now()
        ]);

        // Create multiple logs
        $this->authService->logLogin($user, '127.0.0.1', 'Browser 1', 'web');
        $this->authService->logLogin($user, '*********', 'Browser 2', 'mobile');

        $logs = $this->authService->getUserAuthenticationLogs($user, 5);

        $this->assertCount(2, $logs);
        $this->assertEquals('*********', $logs->first()->ip_address); // Most recent first
    }

    /** @test */
    public function can_clean_old_logs()
    {
        $user = User::create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'display_name' => 'Test User',
            'type' => 'resume',
            'email_verified_at' => now()
        ]);

        // Create old log
        $oldLog = $this->authService->logLogin($user, '127.0.0.1', 'Browser', 'web');
        $oldLog->update(['login_at' => now()->subDays(100)]);

        // Create recent log
        $this->authService->logLogin($user, '*********', 'Browser', 'web');

        $deletedCount = $this->authService->cleanOldLogs(90);

        $this->assertEquals(1, $deletedCount);
        $this->assertDatabaseMissing('authentication_logs', [
            'id' => $oldLog->id
        ]);
    }

    /** @test */
    public function logs_failed_login_attempts()
    {
        $this->authService->logFailedLogin(
            '<EMAIL>',
            '127.0.0.1',
            'Mozilla/5.0 Test Browser',
            'invalid_password'
        );

        // Since we're just logging, we can't assert database records
        // But we can verify no exceptions were thrown
        $this->assertTrue(true);
    }
}
