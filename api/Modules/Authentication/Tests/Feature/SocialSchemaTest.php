<?php

namespace Modules\Authentication\Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Schema;
use Modules\Authentication\Entities\Social;
use Modules\User\Entities\User;

class SocialSchemaTest extends TestCase
{
    use RefreshDatabase;

    /** @test */
    public function socials_table_has_correct_schema()
    {
        // Check if table exists
        $this->assertTrue(Schema::hasTable('socials'));

        // Check if all required columns exist
        $this->assertTrue(Schema::hasColumn('socials', 'id'));
        $this->assertTrue(Schema::hasColumn('socials', 'user_id'));
        $this->assertTrue(Schema::hasColumn('socials', 'provider'));
        $this->assertTrue(Schema::hasColumn('socials', 'provider_user_id'));
        $this->assertTrue(Schema::hasColumn('socials', 'name'));
        $this->assertTrue(Schema::hasColumn('socials', 'email'));
        $this->assertTrue(Schema::hasColumn('socials', 'avatar'));
        $this->assertTrue(Schema::hasColumn('socials', 'created_at'));
        $this->assertTrue(Schema::hasColumn('socials', 'updated_at'));

        // Check that old column name doesn't exist
        $this->assertFalse(Schema::hasColumn('socials', 'provider_id'));
    }

    /** @test */
    public function social_model_uses_correct_fillable_fields()
    {
        $social = new Social();
        $fillable = $social->getFillable();

        $this->assertContains('user_id', $fillable);
        $this->assertContains('provider', $fillable);
        $this->assertContains('provider_user_id', $fillable);
        $this->assertContains('name', $fillable);
        $this->assertContains('email', $fillable);
        $this->assertContains('avatar', $fillable);

        // Check that old field name is not in fillable
        $this->assertNotContains('provider_id', $fillable);
    }

    /** @test */
    public function can_create_social_record_with_provider_user_id()
    {
        $user = User::create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'display_name' => 'Test User',
            'type' => 'resume',
            'email_verified_at' => now()
        ]);

        $social = Social::create([
            'user_id' => $user->id,
            'provider' => 'facebook',
            'provider_user_id' => '123456789',
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'avatar' => 'https://facebook.com/avatar.jpg',
        ]);

        $this->assertDatabaseHas('socials', [
            'user_id' => $user->id,
            'provider' => 'facebook',
            'provider_user_id' => '123456789',
            'name' => 'John Doe',
            'email' => '<EMAIL>',
        ]);

        $this->assertEquals('123456789', $social->provider_user_id);
        $this->assertEquals('facebook', $social->provider);
    }

    /** @test */
    public function unique_constraint_works_on_provider_and_provider_user_id()
    {
        $user1 = User::create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'display_name' => 'User 1',
            'type' => 'resume',
            'email_verified_at' => now()
        ]);

        $user2 = User::create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'display_name' => 'User 2',
            'type' => 'resume',
            'email_verified_at' => now()
        ]);

        // Create first social record
        Social::create([
            'user_id' => $user1->id,
            'provider' => 'facebook',
            'provider_user_id' => '123456789',
            'name' => 'John Doe',
            'email' => '<EMAIL>',
        ]);

        // Try to create duplicate - should fail
        $this->expectException(\Illuminate\Database\QueryException::class);
        
        Social::create([
            'user_id' => $user2->id,
            'provider' => 'facebook',
            'provider_user_id' => '123456789', // Same provider_user_id
            'name' => 'Jane Doe',
            'email' => '<EMAIL>',
        ]);
    }

    /** @test */
    public function can_have_same_provider_user_id_for_different_providers()
    {
        $user = User::create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'display_name' => 'Test User',
            'type' => 'resume',
            'email_verified_at' => now()
        ]);

        // Create Facebook social record
        $facebookSocial = Social::create([
            'user_id' => $user->id,
            'provider' => 'facebook',
            'provider_user_id' => '123456789',
            'name' => 'John Doe',
            'email' => '<EMAIL>',
        ]);

        // Create Twitter social record with same provider_user_id (should work)
        $twitterSocial = Social::create([
            'user_id' => $user->id,
            'provider' => 'twitter',
            'provider_user_id' => '123456789', // Same ID but different provider
            'name' => 'John Doe',
            'email' => '<EMAIL>',
        ]);

        $this->assertDatabaseHas('socials', [
            'provider' => 'facebook',
            'provider_user_id' => '123456789',
        ]);

        $this->assertDatabaseHas('socials', [
            'provider' => 'twitter',
            'provider_user_id' => '123456789',
        ]);

        $this->assertEquals(2, Social::where('provider_user_id', '123456789')->count());
    }
}
