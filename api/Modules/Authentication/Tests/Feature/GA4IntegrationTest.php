<?php

namespace Modules\Authentication\Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Http;
use Modules\User\Entities\User;
use Modules\User\Events\SignUp;
use Modules\Authentication\Events\AuthenticationLogCreated;
use Modules\Authentication\Entities\AuthenticationLog;

class GA4IntegrationTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Mock GA4 configuration
        config([
            'services.ga4' => [
                [
                    'measurement_id' => 'G-TEST123',
                    'api_secret' => 'test_secret',
                ]
            ]
        ]);
    }

    /** @test */
    public function signup_event_implements_ga4_interface()
    {
        $user = User::create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'display_name' => 'Test User',
            'type' => 'resume',
            'email_verified_at' => now()
        ]);

        $event = new SignUp($user, ['method' => 'email']);

        // Test GA4EventInterface implementation
        $this->assertInstanceOf(\App\Contracts\GA4EventInterface::class, $event);
        
        $ga4Data = $event->getGa4EventData('G-TEST123');
        
        $this->assertArrayHasKey('name', $ga4Data);
        $this->assertArrayHasKey('params', $ga4Data);
        $this->assertEquals('sign_up_server', $ga4Data['name']);
        $this->assertEquals($user->id, $ga4Data['params']['user_id']);
        $this->assertEquals('email', $ga4Data['params']['method']);
    }

    /** @test */
    public function signup_event_implements_should_broadcast_to_analytics()
    {
        $user = User::create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'display_name' => 'Test User',
            'type' => 'resume',
            'email_verified_at' => now()
        ]);

        $event = new SignUp($user);

        // Test ShouldBroadcastToAnalytics implementation
        $this->assertInstanceOf(\App\Contracts\ShouldBroadcastToAnalytics::class, $event);
        
        // Mock analytics object
        $analytics = $this->createMock(\stdClass::class);
        $analytics->expects($this->once())->method('setEventValue')->willReturnSelf();
        $analytics->expects($this->once())->method('setEventCategory')->willReturnSelf();
        $analytics->expects($this->once())->method('setEventAction')->willReturnSelf();
        
        $event->withAnalytics($analytics);
    }

    /** @test */
    public function authentication_log_created_event_implements_interfaces()
    {
        $user = User::create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'display_name' => 'Test User',
            'type' => 'resume',
            'email_verified_at' => now()
        ]);

        $authLog = new AuthenticationLog([
            'authenticatable_id' => $user->id,
            'authenticatable_type' => User::class,
            'ip_address' => '127.0.0.1',
            'user_agent' => 'Test Agent',
            'login_at' => now(),
            'type_login' => 'web',
            'client_id' => 1,
        ]);

        $event = new AuthenticationLogCreated($authLog);

        // Test both interfaces
        $this->assertInstanceOf(\App\Contracts\GA4EventInterface::class, $event);
        $this->assertInstanceOf(\App\Contracts\ShouldBroadcastToAnalytics::class, $event);
        
        $ga4Data = $event->getGa4EventData('G-TEST123');
        
        $this->assertEquals('login', $ga4Data['name']);
        $this->assertEquals($user->id, $ga4Data['params']['user_id']);
        $this->assertEquals('web', $ga4Data['params']['login_type']);
    }

    /** @test */
    public function ga4_helper_functions_work_correctly()
    {
        // Test get_ga4_session function
        $sessionData = get_ga4_session('G-TEST123');
        
        $this->assertArrayHasKey('session_id', $sessionData);
        $this->assertArrayHasKey('engagement_time_msec', $sessionData);
        $this->assertArrayHasKey('ga_session_id', $sessionData);
        $this->assertArrayHasKey('ga_session_number', $sessionData);
        
        // Test get_ga_client_id function
        $clientId = get_ga_client_id();
        $this->assertEquals('topdev_system', $clientId); // Default when no cookie
    }

    /** @test */
    public function send_ga4_event_makes_http_request()
    {
        Http::fake([
            'www.google-analytics.com/*' => Http::response([], 200)
        ]);

        $eventData = [
            'name' => 'test_event',
            'params' => [
                'user_id' => 'test_user',
                'value' => 1,
            ]
        ];

        send_ga4_event($eventData, 'test_client_id');

        Http::assertSent(function ($request) {
            return str_contains($request->url(), 'www.google-analytics.com/mp/collect') &&
                   str_contains($request->url(), 'measurement_id=G-TEST123') &&
                   str_contains($request->url(), 'api_secret=test_secret');
        });
    }

    /** @test */
    public function ga4_events_are_disabled_when_config_is_false()
    {
        config(['authentication.analytics.ga4_enabled' => false]);
        
        Http::fake();

        $eventData = [
            'name' => 'test_event',
            'params' => ['user_id' => 'test_user']
        ];

        send_ga4_event($eventData);

        Http::assertNothingSent();
    }

    /** @test */
    public function ga4_event_dispatcher_handles_events()
    {
        Http::fake([
            'www.google-analytics.com/*' => Http::response([], 200)
        ]);

        $user = User::create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'display_name' => 'Test User',
            'type' => 'resume',
            'email_verified_at' => now()
        ]);

        $event = new SignUp($user, ['method' => 'email']);

        \App\Services\GA4EventDispatcher::dispatch($event);

        Http::assertSent(function ($request) {
            $body = json_decode($request->body(), true);
            return isset($body['events'][0]['name']) && 
                   $body['events'][0]['name'] === 'sign_up_server';
        });
    }
}
