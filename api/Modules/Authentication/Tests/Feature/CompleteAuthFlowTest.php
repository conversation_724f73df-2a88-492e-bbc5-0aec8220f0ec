<?php

namespace Modules\Authentication\Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Queue;
use Modules\User\Entities\User;
use Modules\Authentication\Entities\Social;
use Modules\Authentication\Events\AuthenticationLogCreated;
use Modules\Authentication\Events\UserFirstLogin;
use Modules\User\Events\NewUserProcessed;
use Modules\User\Events\SignUp;
use Modules\Authentication\Jobs\DispatchAuthAnalyticsJob;
use Modules\Authentication\Jobs\CreateFcmDeviceTokenActivity;

class CompleteAuthFlowTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Fake events and queues for testing
        Event::fake();
        Queue::fake();
    }

    /** @test */
    public function complete_registration_flow_fires_correct_events()
    {
        $userData = [
            'email' => '<EMAIL>',
            'password' => 'password123',
            'password_confirmation' => 'password123',
            'display_name' => 'Test User',
            'type' => 'resume'
        ];

        $response = $this->postJson('/api/auth/register', $userData);

        $response->assertStatus(201);

        // Assert events were fired
        Event::assertDispatched(\Illuminate\Auth\Events\Registered::class);
        Event::assertDispatched(NewUserProcessed::class);
        Event::assertDispatched(SignUp::class);

        // Assert jobs were dispatched
        Queue::assertPushed(DispatchAuthAnalyticsJob::class, function ($job) {
            return $job->eventType === 'register';
        });

        // Assert user was created
        $this->assertDatabaseHas('users', [
            'email' => '<EMAIL>',
            'display_name' => 'Test User',
            'type' => 'resume'
        ]);
    }

    /** @test */
    public function complete_login_flow_fires_correct_events()
    {
        // Create user
        $user = User::create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password123'),
            'display_name' => 'Test User',
            'type' => 'resume',
            'email_verified_at' => now()
        ]);

        $loginData = [
            'email' => '<EMAIL>',
            'password' => 'password123'
        ];

        $response = $this->postJson('/api/auth/login', $loginData);

        $response->assertStatus(200);

        // Assert events were fired
        Event::assertDispatched(\Illuminate\Auth\Events\Login::class);
        
        // Assert jobs were dispatched
        Queue::assertPushed(DispatchAuthAnalyticsJob::class, function ($job) {
            return $job->eventType === 'login';
        });
    }

    /** @test */
    public function social_login_creates_user_and_social_link()
    {
        // Mock social user data
        $socialUserData = [
            'id' => '*********',
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'avatar' => 'https://example.com/avatar.jpg'
        ];

        // This would require mocking Socialite, but shows the expected flow
        $this->markTestSkipped('Requires Socialite mocking setup');

        // Expected behavior:
        // 1. User should be created if doesn't exist
        // 2. Social link should be created
        // 3. Events should be fired
        // 4. Analytics should be tracked
    }

    /** @test */
    public function first_login_triggers_welcome_notifications()
    {
        // Create user
        $user = User::create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password123'),
            'display_name' => 'Test User',
            'type' => 'resume',
            'email_verified_at' => now()
        ]);

        // Simulate first login by creating authentication log
        $authLog = new \Modules\Authentication\Entities\AuthenticationLog([
            'authenticatable_id' => $user->id,
            'authenticatable_type' => get_class($user),
            'ip_address' => '127.0.0.1',
            'user_agent' => 'Test Agent',
            'login_at' => now(),
            'login_nth' => 1,
            'type_login' => 'api',
            'client_id' => config('authentication.client_mobile'),
        ]);

        // Fire the event
        event(new UserFirstLogin($authLog, 'test_device_token'));

        // Assert welcome notification job was queued
        Queue::assertPushed(\Modules\Authentication\Jobs\CreateFcmDeviceTokenActivity::class);
    }

    /** @test */
    public function user_can_link_and_unlink_social_accounts()
    {
        $user = User::create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password123'),
            'display_name' => 'Test User',
            'type' => 'resume'
        ]);

        $token = $user->createToken('Test Token')->accessToken;

        // Test linking social account
        Social::create([
            'user_id' => $user->id,
            'provider' => 'facebook',
            'provider_user_id' => '*********',
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'avatar' => 'https://facebook.com/avatar.jpg',
        ]);

        // Get linked accounts
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->getJson('/api/auth/social/accounts');

        $response->assertStatus(200)
                ->assertJsonStructure([
                    'success',
                    'data' => [
                        'linked_accounts' => [
                            '*' => [
                                'provider',
                                'provider_user_id',
                                'name',
                                'email'
                            ]
                        ]
                    ]
                ]);

        // Test unlinking
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->deleteJson('/api/auth/social/facebook/unlink');

        $response->assertStatus(200);

        // Assert social link was removed
        $this->assertDatabaseMissing('socials', [
            'user_id' => $user->id,
            'provider' => 'facebook'
        ]);
    }

    /** @test */
    public function logout_revokes_all_user_tokens()
    {
        $user = User::create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password123'),
            'display_name' => 'Test User',
            'type' => 'resume'
        ]);

        $token = $user->createToken('Test Token')->accessToken;

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->postJson('/api/auth/logout');

        $response->assertStatus(200)
                ->assertJson([
                    'success' => true,
                    'message' => 'Logout successful'
                ]);

        // Try to access protected route with revoked token
        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->getJson('/api/auth/me');

        $response->assertStatus(401);
    }

    /** @test */
    public function frozen_user_cannot_login()
    {
        $user = User::create([
            'email' => '<EMAIL>',
            'password' => bcrypt('password123'),
            'display_name' => 'Test User',
            'type' => 'resume',
            'freeze_at' => now(), // User is frozen
        ]);

        $loginData = [
            'email' => '<EMAIL>',
            'password' => 'password123'
        ];

        $response = $this->postJson('/api/auth/login', $loginData);

        $response->assertStatus(403)
                ->assertJson([
                    'success' => false,
                    'message' => 'Account is frozen'
                ]);
    }
}
