<?php

namespace Modules\Authentication\Notifications;

use App\Channels\Messages\TelegramMessage;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Notification;
use Illuminate\Support\Facades\Log;

class NotificationEmployerLogin extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        // For now, just log. Can be extended to Telegram, Slack, etc.
        return ['database'];
    }

    /**
     * Get the array representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toArray($notifiable): array
    {
        $companyName = $notifiable->company->display_name ?? 'Unknown Company';
        $companyId = $notifiable->company->id ?? 0;
        $email = $notifiable->email;
        
        Log::info('Employer Login Notification', [
            'user_id' => $notifiable->id,
            'email' => $email,
            'company_name' => $companyName,
            'company_id' => $companyId,
            'login_time' => now(),
        ]);

        return [
            'type' => 'employer_login',
            'user_id' => $notifiable->id,
            'email' => $email,
            'company_name' => $companyName,
            'company_id' => $companyId,
            'message' => "🏢 {$companyName} ({$companyId}) vừa mới đăng nhập",
            'login_time' => now(),
        ];
    }

    /**
     * Get the Telegram representation of the notification.
     * (Can be implemented when Telegram channel is available)
     *
     * @param  mixed  $notifiable
     * @return string
     */
    public function toTelegram($notifiable)
    {
        $companyName = $notifiable->company->display_name;
        $companyId = $notifiable->company->id;
        $email = $notifiable->email;
        $link = frontend_url('nha-tuyen-dung/' . $notifiable->company->slug . '-' . $companyId);
        $receiver = config('services.telegram-bot-api.receivers');

        return TelegramMessage::create()
            ->to($receiver)
            ->content(
                'Send employer login information'
                . "\n\n" .
                '📅 ' . now()
                . "\n" .
                '🏢 ' . $companyName
                . ' (' . $companyId . ') vừa mới đăng nhập'
                . "\n" .
                '👤 By email: ' . $email
                . "\n" .
                'Link: ' . $link
            )
            ->options(['disable_web_page_preview' => true]);
    }

    /**
     * Get the Google Chat representation of the notification.
     * (Can be implemented when Google Chat channel is available)
     *
     * @param $notifiable
     * @return string
     */
    public function toGoogleChat($notifiable)
    {
        $companyName = $notifiable->company->display_name;
        $companyId = $notifiable->company->id;
        $email = $notifiable->email;
        $link = frontend_url('nha-tuyen-dung/' . $notifiable->company->slug . '-' . $companyId);

        return 'Send employer login information'
            . "\n\n" .
            '📅 ' . now()
            . "\n" .
            '🏢 ' . $companyName
            . ' (' . $companyId . ') vừa mới đăng nhập'
            . "\n" .
            '👤 By email: ' . $email
            . "\n" .
            'Link: ' . $link;
    }
}
