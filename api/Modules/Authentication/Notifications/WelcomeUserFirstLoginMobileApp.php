<?php

namespace Modules\Authentication\Notifications;

use Illuminate\Notifications\Notification;
use Modules\Authentication\Entities\AuthenticationLog;

class WelcomeUserFirstLoginMobileApp extends Notification
{
    protected $authenlog;
    protected $device_token;

    public function __construct(AuthenticationLog $authenlog, $device_token = null)
    {
        $this->authenlog = $authenlog;
        $this->device_token = $device_token;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return [
            'database',
            // Add FCM channel when available
        ];
    }

    /**
     * Get the database representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toDatabase($notifiable)
    {
        return [
            'title' => 'Chào bạn ' . ($notifiable->full_name ?? $notifiable->display_name) . '. Cảm ơn bạn đã đăng nhập trên TopDev. TopDev sẽ giúp bạn:',
            'body' => '- Tạo CV chuẩn Developer >>' . "\n" . '- T<PERSON><PERSON> kiếm việc làm IT phù hợp >>' . "\n" . '- Khám phá môi trường các công ty IT tại Việt Nam >>',
            'type' => 'Homepage',
            'action_url' => config('app.frontend_url'),
            'device_token' => $this->device_token,
        ];
    }

    /**
     * Get the FCM representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function toFcm($notifiable)
    {
        return [
            'title' => 'Chào bạn ' . ($notifiable->full_name ?? $notifiable->display_name) . '. Cảm ơn bạn đã đăng nhập trên TopDev. TopDev sẽ giúp bạn:',
            'body' => '- Tạo CV chuẩn Developer >>' . "\n" . '- Tìm kiếm việc làm IT phù hợp >>' . "\n" . '- Khám phá môi trường các công ty IT tại Việt Nam >>',
            'data' => [
                'type' => 'Homepage',
                'action_url' => config('app.frontend_url'),
            ],
            'token' => $this->device_token,
        ];
    }
}
