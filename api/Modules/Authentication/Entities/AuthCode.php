<?php

namespace Modules\Authentication\Entities;

use <PERSON><PERSON>\Passport\AuthCode as BaseAuthCode;
use GeneaLabs\LaravelModelCaching\Traits\Cachable;

class AuthCode extends BaseAuthCode
{
    //use Cachable;

    /**
     * The connection name for the model.
     *
     * @var string
     */
    protected $connection = 'mysql_accounts';

    /**
     * The cache prefix for the model.
     *
     * @var string
     */
    protected $cachePrefix = "auth-code";

    /**
     * The number of seconds to cache the model.
     *
     * @var int
     */
    protected $cacheCooldownSeconds = 300;
}
