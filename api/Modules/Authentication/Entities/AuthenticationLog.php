<?php

namespace Modules\Authentication\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use <PERSON><PERSON><PERSON>\Agent\Agent;
use <PERSON><PERSON>\Scout\Searchable;

/**
 * Class AuthenticationLog
 *
 * @property int $id
 * @property int $login_nth
 * @property string $ip_address
 * @property string $user_agent
 * @property \Carbon\Carbon|null $login_at
 * @property \Carbon\Carbon|null $logout_at
 * @property string|null $client_id
 * @property string|null $type_login
 * @property string $authenticatable_type
 * @property int $authenticatable_id
 */
class AuthenticationLog extends Model
{
    use Searchable;

    /**
     * @inheritdoc
     */
    protected $connection = 'mysql_accounts';

    /**
     * @inheritdoc
     */
    protected $fillable = [
        'login_nth',
        'ip_address',
        'user_agent',
        'login_at',
        'logout_at',
        'client_id',
        'type_login',
    ];

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'authentication_log';

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;

    /**
     * The attributes that aren't mass assignable.
     *
     * @var array
     */
    protected $guarded = ['authenticatable_id', 'authenticatable_type'];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'login_at' => 'datetime',
        'logout_at' => 'datetime',
    ];

    /**
     * Register any events for your application.
     *
     * @return void
     */
    protected static function boot(): void
    {
        parent::boot();
    }

    /**
     * Get the authenticatable entity that the authentication log belongs to.
     */
    public function authenticatable(): MorphTo
    {
        return $this->morphTo();
    }

    public function authenLogSameOwner()
    {
        return $this->hasMany($this, 'authenticatable_id', 'authenticatable_id')
            ->whereNotNull('login_at')
            ->where('id', '<>', $this->getKey());
    }

    /**
     * Get the indexable data array for the model.
     *
     * @return array
     */
    public function toSearchableArray()
    {
        \Log::info('Build elasticsearch database authen_log: ' . $this->getKey());
        // Customize the data array...

        return [
            'id' => $this->getKey(),
            'authenticatable_type' => $this->authenticatable_type,
            'authenticatable_id' => $this->authenticatable_id,
            'ip_address' => $this->ip_address,
            'client_id' => $this->client_id,
            'type_login' => $this->type_login,
            'user_agent' => $this->user_agent,
            'agent_platform' => $this->agent_platform,
            'login_at' => empty($this->login_at) ? null : $this->login_at->format('Y-m-d H:i:s'),
            'logout_at' => empty($this->logout_at) ? null : $this->logout_at->format('Y-m-d H:i:s'),
            'login_nth' => $this->login_nth,
        ];
    }

    /**
     * Get the name of the index associated with the model.
     *
     * @return string
     */
    public function searchableAs()
    {
        return 'authentication_log';
    }

    /**
     * Get the platform of the user agent.
     * Accessor for the 'agent_platform' attribute. (->agent_platform)
     *
     * @return string
     */
    public function getAgentPlatformAttribute(): string
    {
        $agent = new Agent();
        $agent->setUserAgent($this->user_agent);

        return $agent->platform();
    }
}
