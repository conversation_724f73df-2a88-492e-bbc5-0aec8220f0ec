<?php

namespace Modules\Authentication\Entities;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Modules\User\Entities\User;

class Social extends Model
{
    /**
     * @inheritdoc
     */
    protected $connection = 'mysql_accounts';

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'socials';

    protected $fillable = [
        'user_id',
        'provider_user_id',
        'provider',
        'email',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}
