<?php

namespace Modules\Authentication\Entities;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Laravel\Passport\Token as BaseToken;
use Illuminate\Database\Eloquent\Builder;

/**
 * Class Token
 *
 * @property int $id
 * @property string $name
 * @property string $scopes
 * @property int $client_id
 * @property int $user_id
 * @property int $revoked
 * @property Carbon|null $created_at
 * @property Carbon|null $updated_at
 * @property Client $client
 * @property RefreshToken|null $refreshToken
 * @property Collection $refreshTokens
 * @method static Builder|Token alive()
 * @method static Builder|Token whereId($value)
 * @method static Builder|Token whereName($value)
 * @method static Builder|Token whereScopes($value)
 * @method static Builder|Token whereRevoked($value)
 * @method static Builder|Token whereCreatedAt($value)
 * @method static Builder|Token whereUpdatedAt($value)
 * @method static Builder|Token whereClientId($value)
 * @mixin \Eloquent
 */
class Token extends BaseToken
{
    //use Cachable;

    public const STATUS_ALIVE = 0;
    public const STATUS_REVOKED = 1;

    /**
     * The connection name for the model.
     *
     * @var string
     */
    protected $connection = 'mysql_accounts';

    protected $fillable = [
        'name',
        'scopes',
        'revoked',
        'client_id',
        'user_id',
    ];

    protected $cachePrefix = "token";

    protected $cacheCooldownSeconds = 300;

    /**
     * Determine if the client should skip the authorization prompt.
     *
     * @return bool
     */
    public function refreshToken()
    {
        return $this->hasOne(RefreshToken::class, 'access_token_id');
    }

    public function clientPasswordNullable()
    {
        return $this->client->passwordCanNullable();
    }

    public function scopeAlive(Builder $builder)
    {
        return $builder->where('revoked', self::STATUS_ALIVE);
    }
}
