<?php

namespace Modules\Authentication\Resolvers;

use Illuminate\Http\Request;
use Modules\User\Entities\User;

class UserResolver
{
    /**
     * Resolve user from request
     *
     * @param Request $request
     * @return User|null
     */
    public static function resolve(Request $request): ?User
    {
        // Try to get user from authenticated session
        if ($request->user()) {
            return $request->user();
        }

        // Try to get user from token in cookie
        $token = $request->cookie('topdev_token');
        if ($token) {
            return self::resolveFromToken($token);
        }

        // Try to get user from Authorization header
        $authHeader = $request->header('Authorization');
        if ($authHeader && str_starts_with($authHeader, 'Bearer ')) {
            $token = substr($authHeader, 7);
            return self::resolveFromToken($token);
        }

        return null;
    }

    /**
     * Resolve user from access token
     *
     * @param string $token
     * @return User|null
     */
    protected static function resolveFromToken(string $token): ?User
    {
        try {
            // Find token in database
            $tokenModel = \Laravel\Passport\Token::where('id', $token)
                ->where('revoked', false)
                ->where('expires_at', '>', now())
                ->first();

            if (!$tokenModel) {
                return null;
            }

            // Get user from token
            return User::find($tokenModel->user_id);
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * Resolve user from user ID in cookie
     *
     * @param Request $request
     * @return User|null
     */
    public static function resolveFromUserId(Request $request): ?User
    {
        $userId = $request->cookie('TDUID');
        
        if ($userId) {
            return User::find($userId);
        }

        return null;
    }

    /**
     * Check if user is authenticated via any method
     *
     * @param Request $request
     * @return bool
     */
    public static function isAuthenticated(Request $request): bool
    {
        return self::resolve($request) !== null;
    }

    /**
     * Get user type from request
     *
     * @param Request $request
     * @return string|null
     */
    public static function getUserType(Request $request): ?string
    {
        $user = self::resolve($request);
        return $user ? $user->type : null;
    }

    /**
     * Check if current user is employer
     *
     * @param Request $request
     * @return bool
     */
    public static function isEmployer(Request $request): bool
    {
        $user = self::resolve($request);
        return $user ? $user->isEmployer() : false;
    }
}
