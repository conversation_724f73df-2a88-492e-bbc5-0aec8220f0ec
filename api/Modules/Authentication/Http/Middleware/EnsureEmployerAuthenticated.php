<?php

namespace Modules\Authentication\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Modules\Authentication\Resolvers\UserResolver;

class EnsureEmployerAuthenticated
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        $user = UserResolver::resolve($request);

        // Check if user is authenticated
        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthenticated'
            ], 401);
        }

        // Check if user is employer
        if (!$user->isEmployer()) {
            return response()->json([
                'success' => false,
                'message' => 'Access denied. Employer account required.'
            ], 403);
        }

        // Check if employer account is approved
        if (!$user->hasApprovedAccount()) {
            return response()->json([
                'success' => false,
                'message' => 'Employer account is not approved yet'
            ], 403);
        }

        // Check if account is frozen
        if ($user->isFreezing()) {
            return response()->json([
                'success' => false,
                'message' => 'Account is frozen'
            ], 403);
        }

        return $next($request);
    }
}
