<?php

namespace Modules\Authentication\Http\Controllers;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Laravel\Passport\RefreshTokenRepository;
use Laravel\Passport\TokenRepository;
use Modules\Authentication\Events\SignUp;
use Modules\Authentication\Http\Requests\LoginRequest;
use Modules\Authentication\Http\Requests\RegisterRequest;
use Modules\Authentication\Services\TokenCookieService;
use Modules\User\Entities\User;

class AuthenticationController extends Controller
{
    protected $tokenRepository;
    protected $refreshTokenRepository;
    protected $tokenCookieService;

    public function __construct(
        TokenRepository $tokenRepository,
        RefreshTokenRepository $refreshTokenRepository,
        TokenCookieService $tokenCookieService
    ) {
        $this->tokenRepository = $tokenRepository;
        $this->refreshTokenRepository = $refreshTokenRepository;
        $this->tokenCookieService = $tokenCookieService;
    }

    /**
     * Login user
     * @param LoginRequest $request
     * @return JsonResponse
     */
    public function login(LoginRequest $request): JsonResponse
    {
        $credentials = $request->only('email', 'password');

        if (Auth::attempt($credentials)) {
            $user = Auth::user();

            // Check if user is frozen
            if ($user->isFreezing()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Account is frozen'
                ], 403);
            }

            // Check if employer needs account approval
            /*if ($user->isEmployer() && !$user->hasApprovedAccount()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Employer account is not approved yet'
                ], 403);
            }*/

            // Create access token
            $token = $user->createToken('API Token')->accessToken;

            // Set cookies for multi-subdomain access
            $this->tokenCookieService->setAllAuthCookies($token, null, $user->id);

            // Fire login event
            event(new \Illuminate\Auth\Events\Login('api', $user, false));

            // Send employer login notification if user is employer
            if ($user->isEmployer()) {
                $user->sendEmployerLoginNotification();

                Log::info('Employer logged in', [
                    'user_id' => $user->id,
                    'email' => $user->email,
                    'company_id' => $user->company_id ?? null,
                    'ip' => $request->ip(),
                    'user_agent' => $request->userAgent(),
                ]);
            }

            // Log successful login
            Log::info('User logged in successfully', [
                'user_id' => $user->id,
                'email' => $user->email,
                'type' => $user->type,
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
            ]);

            return response()->json([
                'success' => true,
                'message' => 'Login successful',
                'data' => [
                    'user' => [
                        'id' => $user->id,
                        'email' => $user->email,
                        'display_name' => $user->display_name,
                        'type' => $user->type,
                        'is_employer' => $user->isEmployer(),
                        'has_verified_email' => $user->hasVerifiedEmail(),
                        'has_approved_account' => $user->hasApprovedAccount(),
                    ],
                    'access_token' => $token,
                    'token_type' => 'Bearer',
                ]
            ]);
        }

        // Log failed login attempt
        Log::warning('Failed login attempt', [
            'email' => $request->email,
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
        ]);

        return response()->json([
            'success' => false,
            'message' => 'Invalid credentials'
        ], 401);
    }

    /**
     * Register user
     * @param RegisterRequest $request
     * @return JsonResponse
     */
    public function register(RegisterRequest $request)
    {
        $user = User::query()->create([
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'display_name' => $request->display_name,
            'type' => $request->type ?? User::RESUME_TYPE,
            'email_verified_at' => now(),
        ]);

        $token = $user->createToken('API Token')->accessToken;

        // Fire registration events (match accounts exactly)
        event(new \Illuminate\Auth\Events\Registered($user));
        event(new \Modules\User\Events\NewUserProcessed($user));
        event(new SignUp($user, [
            'method' => 'email_password',
            'user_agent' => $request->userAgent(),
            'ip_address' => $request->ip(),
        ]));

        // TODO: Dispatch analytics job when implemented
        // dispatch(new \Modules\Authentication\Jobs\DispatchAuthAnalyticsJob($user, 'register', [
        //     'registration_method' => 'email_password',
        //     'user_agent' => $request->userAgent(),
        //     'ip_address' => $request->ip(),
        // ]));

        return response()->json([
            'success' => true,
            'message' => 'Registration successful',
            'data' => [
                'user' => $user,
                'access_token' => $token,
                'token_type' => 'Bearer',
            ]
        ], 201);
    }

    /**
     * Logout user
     * @param Request $request
     * @return JsonResponse
     */
    public function logout(Request $request): JsonResponse
    {
        $user = $request->user();

        if ($user) {
            // Log logout
            Log::info('User logged out', [
                'user_id' => $user->id,
                'email' => $user->email,
                'type' => $user->type,
                'ip' => $request->ip(),
                'user_agent' => $request->userAgent(),
            ]);

            // Fire logout event (this will handle token revocation via listeners)
            event(new \Illuminate\Auth\Events\Logout('api', $user));

            // Clear all authentication cookies
            $this->tokenCookieService->clearAllCookies();
        }

        return response()->json([
            'success' => true,
            'message' => 'Logout successful'
        ]);
    }

    /**
     * Get current user
     * @param Request $request
     * @return JsonResponse
     */
    public function me(Request $request)
    {
        $user = $request->user();

        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthenticated'
            ], 401);
        }

        return response()->json([
            'success' => true,
            'data' => ['user' => $user]
        ]);
    }

    /**
     * Test endpoint
     * @return JsonResponse
     */
    public function test()
    {
        return response()->json([
            'success' => true,
            'message' => 'Authentication API is working!',
            'timestamp' => now(),
            'version' => '1.0.0'
        ]);
    }
}
