<?php

namespace Modules\Authentication\Http\Controllers;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use <PERSON><PERSON>\Passport\RefreshTokenRepository;
use Laravel\Passport\TokenRepository;
use Modules\Authentication\Events\SignUp;
use Modules\Authentication\Http\Requests\LoginRequest;
use Modules\Authentication\Http\Requests\RegisterRequest;

use Modules\User\Entities\User;

class AuthenticationController extends Controller
{
    protected $tokenRepository;
    protected $refreshTokenRepository;

    public function __construct(
        TokenRepository $tokenRepository,
        RefreshTokenRepository $refreshTokenRepository
    ) {
        $this->tokenRepository = $tokenRepository;
        $this->refreshTokenRepository = $refreshTokenRepository;
    }

    /**
     * Login user
     * @param LoginRequest $request
     * @return JsonResponse
     */
    public function login(LoginRequest $request)
    {
        $credentials = $request->only('email', 'password');

        if (Auth::attempt($credentials)) {
            $user = Auth::user();

            if ($user->isFreezing()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Account is frozen'
                ], 403);
            }

            $token = $user->createToken('API Token')->accessToken;

            // Fire login events
            event(new \Illuminate\Auth\Events\Login('api', $user, false));

            // TODO: Dispatch analytics job when implemented
            // dispatch(new \Modules\Authentication\Jobs\DispatchAuthAnalyticsJob($user, 'login', [
            //     'login_method' => 'email_password',
            //     'user_agent' => $request->userAgent(),
            //     'ip_address' => $request->ip(),
            // ]));

            return response()->json([
                'success' => true,
                'message' => 'Login successful',
                'data' => [
                    'user' => $user,
                    'access_token' => $token,
                    'token_type' => 'Bearer',
                ]
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'Invalid credentials'
        ], 401);
    }

    /**
     * Register user
     * @param RegisterRequest $request
     * @return JsonResponse
     */
    public function register(RegisterRequest $request)
    {
        $user = User::query()->create([
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'display_name' => $request->display_name,
            'type' => $request->type ?? User::RESUME_TYPE,
            'email_verified_at' => now(),
        ]);

        $token = $user->createToken('API Token')->accessToken;

        // Fire registration events
        event(new \Illuminate\Auth\Events\Registered($user));
        event(new \Modules\User\Events\NewUserProcessed($user));
        event(new SignUp($user, [
            'method' => 'email_password',
            'user_agent' => $request->userAgent(),
            'ip_address' => $request->ip(),
        ]));

        // TODO: Dispatch analytics job when implemented
        // dispatch(new \Modules\Authentication\Jobs\DispatchAuthAnalyticsJob($user, 'register', [
        //     'registration_method' => 'email_password',
        //     'user_agent' => $request->userAgent(),
        //     'ip_address' => $request->ip(),
        // ]));

        return response()->json([
            'success' => true,
            'message' => 'Registration successful',
            'data' => [
                'user' => $user,
                'access_token' => $token,
                'token_type' => 'Bearer',
            ]
        ], 201);
    }

    /**
     * Logout user
     * @param Request $request
     * @return JsonResponse
     */
    public function logout(Request $request)
    {
        $user = $request->user();

        if ($user) {
            event(new \Illuminate\Auth\Events\Logout('api', $user));

            // Revoke all tokens for the user
            $tokens = $user->tokens;

            foreach ($tokens as $token) {
                $this->tokenRepository->revokeAccessToken($token->id);
                $this->refreshTokenRepository->revokeRefreshTokensByAccessTokenId($token->id);
            }
        }

        return response()->json([
            'success' => true,
            'message' => 'Logout successful'
        ]);
    }

    /**
     * Get current user
     * @param Request $request
     * @return JsonResponse
     */
    public function me(Request $request)
    {
        $user = $request->user();

        if (!$user) {
            return response()->json([
                'success' => false,
                'message' => 'Unauthenticated'
            ], 401);
        }

        return response()->json([
            'success' => true,
            'data' => ['user' => $user]
        ]);
    }

    /**
     * Test endpoint
     * @return JsonResponse
     */
    public function test()
    {
        return response()->json([
            'success' => true,
            'message' => 'Authentication API is working!',
            'timestamp' => now(),
            'version' => '1.0.0'
        ]);
    }
}
