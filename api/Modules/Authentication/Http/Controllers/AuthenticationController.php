<?php

namespace Modules\Authentication\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Modules\Authentication\Http\Requests\LoginRequest;
use Modules\Authentication\Http\Requests\RegisterRequest;
use Modules\Core\Http\Responses\ApiResponse;
use Modules\User\Entities\User;
use Laravel\Passport\TokenRepository;
use Laravel\Passport\RefreshTokenRepository;

class AuthenticationController extends Controller
{
    protected $tokenRepository;
    protected $refreshTokenRepository;

    public function __construct(
        TokenRepository $tokenRepository,
        RefreshTokenRepository $refreshTokenRepository
    ) {
        $this->tokenRepository = $tokenRepository;
        $this->refreshTokenRepository = $refreshTokenRepository;
    }

    /**
     * Login user
     * @param LoginRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function login(LoginRequest $request)
    {
        $credentials = $request->only('email', 'password');

        if (Auth::attempt($credentials)) {
            $user = Auth::user();

            if ($user->isFreezing()) {
                return ApiResponse::error('Account is frozen', 403);
            }

            $token = $user->createToken('API Token')->accessToken;

            // Fire login events
            event(new \Illuminate\Auth\Events\Login('api', $user, false));

            // Dispatch analytics job
            dispatch(new \Modules\Authentication\Jobs\DispatchAuthAnalyticsJob($user, 'login', [
                'login_method' => 'email_password',
                'user_agent' => $request->userAgent(),
                'ip_address' => $request->ip(),
            ]));

            return ApiResponse::success([
                'user' => $user,
                'access_token' => $token,
                'token_type' => 'Bearer',
            ], 'Login successful');
        }

        return ApiResponse::error('Invalid credentials', 401);
    }

    /**
     * Register user
     * @param RegisterRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function register(RegisterRequest $request)
    {
        $user = User::create([
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'display_name' => $request->display_name,
            'type' => $request->type ?? User::RESUME_TYPE,
            'email_verified_at' => now(),
        ]);

        $token = $user->createToken('API Token')->accessToken;

        // Fire registration events
        event(new \Illuminate\Auth\Events\Registered($user));
        event(new \Modules\User\Events\NewUserProcessed($user));
        event(new \Modules\User\Events\SignUp($user, [
            'method' => 'email_password',
            'user_agent' => $request->userAgent(),
            'ip_address' => $request->ip(),
        ]));

        // Dispatch analytics job
        dispatch(new \Modules\Authentication\Jobs\DispatchAuthAnalyticsJob($user, 'register', [
            'registration_method' => 'email_password',
            'user_agent' => $request->userAgent(),
            'ip_address' => $request->ip(),
        ]));

        return ApiResponse::success([
            'user' => $user,
            'access_token' => $token,
            'token_type' => 'Bearer',
        ], 'Registration successful', 201);
    }

    /**
     * Logout user
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function logout(Request $request)
    {
        $user = $request->user();

        if ($user) {
            // Revoke all tokens for the user
            $tokens = $user->tokens;

            foreach ($tokens as $token) {
                $this->tokenRepository->revokeAccessToken($token->id);
                $this->refreshTokenRepository->revokeRefreshTokensByAccessTokenId($token->id);
            }
        }

        return ApiResponse::success([], 'Logout successful');
    }

    /**
     * Get current user
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function me(Request $request)
    {
        $user = $request->user();

        if (!$user) {
            return ApiResponse::error('Unauthenticated', 401);
        }

        return ApiResponse::success(['user' => $user]);
    }


}
