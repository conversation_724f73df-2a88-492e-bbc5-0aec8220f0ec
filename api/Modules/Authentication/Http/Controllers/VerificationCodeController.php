<?php

namespace Modules\Authentication\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\DB;
use Modules\Authentication\Events\VerifySuccessfully;

use Modules\Authentication\Services\VerificationCodeService;
use Modules\User\Entities\User;

class VerificationCodeController extends Controller
{
    /**
     * The verification code service.
     *
     * @var \Modules\Authentication\Services\VerificationCodeService
     */
    protected $verificationCodeService;

    /**
     * Create a new controller instance.
     *
     * @param  \Modules\Authentication\Services\VerificationCodeService  $verificationCodeService
     * @return void
     */
    public function __construct(VerificationCodeService $verificationCodeService)
    {
        $this->verificationCodeService = $verificationCodeService;
    }

    /**
     * Send verification code.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function send(Request $request)
    {
        $request->validate([
            'verifiable' => 'required|string',
            'type' => 'required|in:email,phone',
        ]);

        $verifiable = $request->input('verifiable');
        $type = $request->input('type');

        // Generate and send verification code using service
        $verificationCode = $this->verificationCodeService->generateAndSend($verifiable, $type);

        return response()->json([
            'success' => true,
            'message' => 'Verification code sent successfully',
            'data' => [
                'verifiable' => $verifiable,
                'type' => $type,
                'expires_in' => config('authentication.verification_code.expires_minutes', 15) * 60,
                'expires_in_minutes' => config('authentication.verification_code.expires_minutes', 15),
            ]
        ]);
    }

    /**
     * Verify the verification code.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function verify(Request $request)
    {
        $request->validate([
            'verifiable' => 'required|string',
            'code' => 'required|string',
            'type' => 'required|in:email,phone',
        ]);

        $verifiable = $request->input('verifiable');
        $code = $request->input('code');
        $type = $request->input('type');

        // Verify code using service
        $isValid = $this->verificationCodeService->verify($verifiable, $code, $type);

        if (!$isValid) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid or expired verification code'
            ], 422);
        }

        // Fire verification successful event
        event(new VerifySuccessfully($verifiable, $type));

        return response()->json([
            'success' => true,
            'message' => 'Verification successful',
            'data' => [
                'verifiable' => $verifiable,
                'type' => $type,
            ]
        ]);
    }
}
