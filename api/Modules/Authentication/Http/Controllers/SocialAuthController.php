<?php

namespace Modules\Authentication\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Laravel\Socialite\Facades\Socialite;
use Modules\Authentication\Events\SignUp;
use Modules\Authentication\Services\TokenCookieService;
use Modules\User\Entities\Social;
use Modules\User\Entities\User;
use Modules\User\Events\NewUserProcessed;

class SocialAuthController extends Controller
{
    /**
     * Supported social providers
     */
    protected $supportedProviders = ['facebook', 'twitter', 'google', 'linkedin', 'github', 'apple'];

    /**
     * Token cookie service
     */
    protected $tokenCookieService;

    public function __construct(TokenCookieService $tokenCookieService)
    {
        $this->tokenCookieService = $tokenCookieService;
    }

    /**
     * Get social login redirect URL
     */
    public function redirectToProvider($provider)
    {
        if (!in_array($provider, $this->supportedProviders)) {
            return response()->json([
                'success' => false,
                'message' => 'Unsupported provider'
            ], 400);
        }

        try {
            $redirectUrl = Socialite::driver($provider)->redirect()->getTargetUrl();

            return response()->json([
                'success' => true,
                'message' => 'Redirect URL generated successfully',
                'data' => [
                    'redirect_url' => $redirectUrl
                ]
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate redirect URL: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Handle social provider callback
     */
    public function handleProviderCallback(Request $request, $provider)
    {
        if (!in_array($provider, $this->supportedProviders)) {
            return response()->json([
                'success' => false,
                'message' => 'Unsupported provider'
            ], 400);
        }

        try {
            // Get user from social provider (sử dụng logic có sẵn)
            $socialUser = Socialite::driver($provider)->user();

            // Find or create user (sử dụng logic có sẵn từ LoginController)
            $user = $this->findOrCreateUser($socialUser, $provider);

            if ($user->isFreezing()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Account is frozen'
                ], 403);
            }

            // Create token
            $token = $user->createToken('API Token')->accessToken;

            // Fire login event
            event(new \Illuminate\Auth\Events\Login('api', $user, false));

            // Set cookies for multi-subdomain access
            $this->tokenCookieService->setAllAuthCookies($token, null, $user->id);

            return response()->json([
                'success' => true,
                'message' => 'Social login successful',
                'data' => [
                    'user' => [
                        'id' => $user->id,
                        'email' => $user->email,
                        'display_name' => $user->display_name,
                        'type' => $user->type,
                    ],
                    'access_token' => $token,
                    'token_type' => 'Bearer',
                    'provider' => $provider,
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Social login failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get linked social accounts
     */
    public function getLinkedAccounts(Request $request)
    {
        $user = $request->user();
        $socials = Social::where('user_id', $user->id)->get();

        return response()->json([
            'success' => true,
            'data' => [
                'linked_accounts' => $socials->map(function ($social) {
                    return [
                        'provider' => $social->provider,
                        'provider_user_id' => $social->provider_user_id,
                        'name' => $social->name,
                        'email' => $social->email,
                        'linked_at' => $social->created_at,
                    ];
                })
            ]
        ]);
    }

    /**
     * Link social account to current user
     */
    public function linkAccount(Request $request, $provider)
    {
        if (!in_array($provider, $this->supportedProviders)) {
            return response()->json([
                'success' => false,
                'message' => 'Unsupported provider'
            ], 400);
        }

        try {
            $socialUser = Socialite::driver($provider)->user();
            $user = $request->user();

            // Check if social account is already linked to another user
            $existingSocial = Social::where('provider', $provider)
                ->where('provider_user_id', $socialUser->getId())
                ->first();

            if ($existingSocial && $existingSocial->user_id !== $user->id) {
                return response()->json([
                    'success' => false,
                    'message' => 'This social account is already linked to another user'
                ], 409);
            }

            // Create or update social link
            Social::updateOrCreate(
                [
                    'user_id' => $user->id,
                    'provider' => $provider,
                ],
                [
                    'provider_user_id' => $socialUser->getId(),
                    'name' => $socialUser->getName(),
                    'email' => $socialUser->getEmail(),
                    'avatar' => $socialUser->getAvatar(),
                ]
            );

            return response()->json([
                'success' => true,
                'message' => 'Social account linked successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to link social account: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Unlink social account
     */
    public function unlinkAccount(Request $request, $provider)
    {
        $user = $request->user();

        $social = Social::where('user_id', $user->id)
            ->where('provider', $provider)
            ->first();

        if (!$social) {
            return response()->json([
                'success' => false,
                'message' => 'Social account not found'
            ], 404);
        }

        $social->delete();

        return response()->json([
            'success' => true,
            'message' => 'Social account unlinked successfully'
        ]);
    }



    /**
     * Find or create user from social provider (sử dụng logic từ LoginController)
     */
    protected function findOrCreateUser($socialUser, $provider)
    {
        return DB::transaction(function () use ($socialUser, $provider) {
            // First, check if social account exists
            $social = Social::where('provider', $provider)
                ->where('provider_user_id', $socialUser->getId())
                ->first();

            if ($social) {
                return $social->user;
            }

            // Check if user exists by email
            $user = User::where('email', $socialUser->getEmail())->first();

            if (!$user) {
                // Create new user
                $user = User::create([
                    'email' => $socialUser->getEmail(),
                    'display_name' => $socialUser->getName() ?: $socialUser->getNickname(),
                    'type' => User::RESUME_TYPE,
                    'email_verified_at' => now(),
                ]);

                // Fire events for new user
                event(new \Illuminate\Auth\Events\Registered($user));
                event(new NewUserProcessed($user));
                event(new SignUp($user, [
                    'method' => $provider,
                    'provider' => $provider,
                ]));
            }

            // Create social link
            Social::create([
                'user_id' => $user->id,
                'provider' => $provider,
                'provider_user_id' => $socialUser->getId(),
                'name' => $socialUser->getName(),
                'email' => $socialUser->getEmail(),
                'avatar' => $socialUser->getAvatar(),
            ]);

            return $user;
        });
    }
}
