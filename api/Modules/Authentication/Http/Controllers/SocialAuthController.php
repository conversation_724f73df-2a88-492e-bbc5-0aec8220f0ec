<?php

namespace Modules\Authentication\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Laravel\Socialite\Facades\Socialite;
use Modules\Authentication\Entities\Social;
use Modules\Core\Http\Responses\ApiResponse;
use Modules\User\Entities\User;

class SocialAuthController extends Controller
{
    /**
     * Redirect to social provider
     * @param string $provider
     * @return \Illuminate\Http\JsonResponse
     */
    public function redirectToProvider($provider)
    {
        try {
            $this->validateProvider($provider);
            
            $redirectUrl = Socialite::driver($provider)->stateless()->redirect()->getTargetUrl();
            
            return ApiResponse::success([
                'redirect_url' => $redirectUrl
            ], 'Redirect URL generated successfully');
            
        } catch (\Exception $e) {
            Log::error("SocialAuthController@redirectToProvider: " . $e->getMessage());
            return ApiResponse::error('Failed to generate redirect URL', 500);
        }
    }

    /**
     * Handle social provider callback
     * @param string $provider
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function handleProviderCallback($provider, Request $request)
    {
        try {
            $this->validateProvider($provider);
            
            // Get user from social provider
            $socialUser = Socialite::driver($provider)->stateless()->user();
            
            if (!$socialUser || !$socialUser->getEmail()) {
                return ApiResponse::error('Unable to get user information from ' . $provider, 400);
            }

            // Find or create user
            $user = $this->findOrCreateUser($socialUser, $provider);
            
            if (!$user) {
                return ApiResponse::error('Failed to create or find user', 500);
            }

            // Check if user is frozen
            if ($user->isFreezing()) {
                return ApiResponse::error('Account is frozen', 403);
            }

            // Create access token
            $token = $user->createToken('Social Login Token')->accessToken;

            return ApiResponse::success([
                'user' => $user,
                'access_token' => $token,
                'token_type' => 'Bearer',
                'provider' => $provider,
            ], 'Social login successful');

        } catch (\Exception $e) {
            Log::error("SocialAuthController@handleProviderCallback: " . $e->getMessage());
            return ApiResponse::error('Social login failed: ' . $e->getMessage(), 500);
        }
    }

    /**
     * Link social account to existing user
     * @param string $provider
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function linkAccount($provider, Request $request)
    {
        try {
            $this->validateProvider($provider);
            
            $user = $request->user();
            if (!$user) {
                return ApiResponse::error('Unauthenticated', 401);
            }

            $socialUser = Socialite::driver($provider)->stateless()->user();
            
            if (!$socialUser) {
                return ApiResponse::error('Unable to get user information from ' . $provider, 400);
            }

            // Check if social account is already linked to another user
            $existingSocial = Social::where('provider', $provider)
                ->where('provider_user_id', $socialUser->getId())
                ->first();

            if ($existingSocial && $existingSocial->user_id !== $user->id) {
                return ApiResponse::error('This social account is already linked to another user', 409);
            }

            // Create or update social link
            Social::updateOrCreate(
                [
                    'user_id' => $user->id,
                    'provider' => $provider,
                ],
                [
                    'provider_user_id' => $socialUser->getId(),
                    'name' => $socialUser->getName(),
                    'email' => $socialUser->getEmail(),
                    'avatar' => $socialUser->getAvatar(),
                ]
            );

            return ApiResponse::success([], 'Social account linked successfully');

        } catch (\Exception $e) {
            Log::error("SocialAuthController@linkAccount: " . $e->getMessage());
            return ApiResponse::error('Failed to link social account', 500);
        }
    }

    /**
     * Unlink social account
     * @param string $provider
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function unlinkAccount($provider, Request $request)
    {
        try {
            $this->validateProvider($provider);
            
            $user = $request->user();
            if (!$user) {
                return ApiResponse::error('Unauthenticated', 401);
            }

            $social = Social::where('user_id', $user->id)
                ->where('provider', $provider)
                ->first();

            if (!$social) {
                return ApiResponse::error('Social account not found', 404);
            }

            $social->delete();

            return ApiResponse::success([], 'Social account unlinked successfully');

        } catch (\Exception $e) {
            Log::error("SocialAuthController@unlinkAccount: " . $e->getMessage());
            return ApiResponse::error('Failed to unlink social account', 500);
        }
    }

    /**
     * Get user's linked social accounts
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getLinkedAccounts(Request $request)
    {
        try {
            $user = $request->user();
            if (!$user) {
                return ApiResponse::error('Unauthenticated', 401);
            }

            $socials = Social::where('user_id', $user->id)->get();

            return ApiResponse::success([
                'linked_accounts' => $socials
            ]);

        } catch (\Exception $e) {
            Log::error("SocialAuthController@getLinkedAccounts: " . $e->getMessage());
            return ApiResponse::error('Failed to get linked accounts', 500);
        }
    }

    /**
     * Validate social provider
     * @param string $provider
     * @throws \InvalidArgumentException
     */
    private function validateProvider($provider)
    {
        $allowedProviders = ['facebook', 'twitter', 'google', 'linkedin', 'github', 'apple'];
        
        if (!in_array($provider, $allowedProviders)) {
            throw new \InvalidArgumentException('Invalid social provider: ' . $provider);
        }
    }

    /**
     * Find or create user from social login
     * @param \Laravel\Socialite\Contracts\User $socialUser
     * @param string $provider
     * @return User|null
     */
    private function findOrCreateUser($socialUser, $provider)
    {
        return DB::transaction(function () use ($socialUser, $provider) {
            // First, check if social account exists
            $social = Social::where('provider', $provider)
                ->where('provider_user_id', $socialUser->getId())
                ->first();

            if ($social) {
                return $social->user;
            }

            // Check if user exists by email
            $user = User::where('email', $socialUser->getEmail())->first();

            if (!$user) {
                // Create new user
                $user = User::create([
                    'email' => $socialUser->getEmail(),
                    'display_name' => $socialUser->getName() ?: $socialUser->getNickname(),
                    'type' => User::RESUME_TYPE,
                    'email_verified_at' => now(),
                ]);
            }

            // Create social link
            Social::create([
                'user_id' => $user->id,
                'provider' => $provider,
                'provider_user_id' => $socialUser->getId(),
                'name' => $socialUser->getName(),
                'email' => $socialUser->getEmail(),
                'avatar' => $socialUser->getAvatar(),
            ]);

            return $user;
        });
    }
}
