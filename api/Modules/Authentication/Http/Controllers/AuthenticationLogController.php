<?php

namespace Modules\Authentication\Http\Controllers;

use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Modules\Authentication\Services\AuthenticationService;

class AuthenticationLogController extends Controller
{
    /**
     * The authentication service.
     *
     * @var AuthenticationService
     */
    protected AuthenticationService $authService;

    /**
     * Create a new controller instance.
     *
     * @param  AuthenticationService  $authService
     *
     * @return void
     */
    public function __construct(AuthenticationService $authService)
    {
        $this->authService = $authService;

        $this->middleware('auth:api')->except([]);
    }

    /**
     * Get the authenticated user's login history.
     *
     * @param  Request  $request
     * @return JsonResponse
     */
    public function index(Request $request)
    {
        $user = $request->user();
        $limit = $request->input('limit', 10);
        
        $logs = $this->authService->getUserAuthenticationLogs($user, $limit);
        
        return response()->json([
            'success' => true,
            'data' => [
                'logs' => $logs
            ]
        ]);
    }

    /**
     * Get the specified login log.
     *
     * @param  int  $id
     *
     * @return JsonResponse
     */
    public function show(int $id)
    {
        $user = request()->user();
        $log = $user->authentications()->where('id', $id)->first();

        if (!$log) {
            return response()->json([
                'success' => false,
                'message' => 'Log not found'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => [
                'log' => $log
            ]
        ]);
    }
}
