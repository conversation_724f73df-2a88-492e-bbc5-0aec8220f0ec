<?php

namespace Modules\Authentication\Services;

use Illuminate\Support\Facades\Log;
use Jen<PERSON><PERSON>\Agent\Agent;
use Modules\Authentication\Entities\AuthenticationLog;
use Modules\Authentication\Events\AuthenticationLogCreated;
use Modules\User\Entities\User;

class AuthenticationService
{
    /**
     * Log a successful login.
     *
     * @param User $user
     * @param string $ip
     * @param string $userAgent
     * @param string $typeLogin
     * @param int|null $clientId
     * @return AuthenticationLog
     */
    public function logLogin(User $user, $ip, $userAgent, $typeLogin = 'web', $clientId = null)
    {
        // Detect device type if not specified
        if ($typeLogin === 'auto') {
            $agent = new Agent();
            $agent->setUserAgent($userAgent);

            if ($agent->isDesktop() || $agent->isRobot()) {
                $typeLogin = 'web';
            } elseif ($agent->isPhone()) {
                $typeLogin = 'mobile';
            } else {
                $typeLogin = 'tablet';
            }
        }

        // Override type_login based on client_id if provided
        if ($clientId && $clientId == config('authentication.client_mobile')) {
            $typeLogin = 'api';
        }

        $authenticationLog = new AuthenticationLog([
            'ip_address' => $ip,
            'user_agent' => $userAgent,
            'login_at' => now(),
            'type_login' => $typeLogin,
            'client_id' => $clientId,
        ]);

        $user->authentications()->save($authenticationLog);

        Log::info('Authentication log created', [
            'user_id' => $user->id,
            'log_id' => $authenticationLog->id,
            'type_login' => $typeLogin,
            'client_id' => $clientId,
        ]);

        // Fire event
        event(new AuthenticationLogCreated($authenticationLog));

        return $authenticationLog;
    }

    /**
     * Log a failed login attempt.
     *
     * @param string $email
     * @param string $ip
     * @param string $userAgent
     * @param string $reason
     * @return void
     */
    public function logFailedLogin($email, $ip, $userAgent, $reason = 'invalid_credentials')
    {
        Log::warning('Failed login attempt', [
            'email' => $email,
            'ip' => $ip,
            'user_agent' => $userAgent,
            'reason' => $reason,
            'timestamp' => now(),
        ]);

        // You can store failed attempts in database if needed
        // For now, just log it
    }

    /**
     * Get authentication logs for a user.
     *
     * @param User $user
     * @param int $limit
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getUserAuthenticationLogs(User $user, $limit = 10)
    {
        return $user->authentications()
            ->orderBy('login_at', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Get recent authentication logs.
     *
     * @param int $limit
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getRecentAuthenticationLogs($limit = 50)
    {
        return AuthenticationLog::with('authenticatable')
            ->orderBy('login_at', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Clean old authentication logs.
     *
     * @param int $daysToKeep
     * @return int Number of deleted records
     */
    public function cleanOldLogs($daysToKeep = 90)
    {
        $cutoffDate = now()->subDays($daysToKeep);
        
        $deletedCount = AuthenticationLog::where('login_at', '<', $cutoffDate)->delete();
        
        Log::info('Cleaned old authentication logs', [
            'deleted_count' => $deletedCount,
            'cutoff_date' => $cutoffDate,
        ]);

        return $deletedCount;
    }
}
