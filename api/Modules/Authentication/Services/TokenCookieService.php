<?php

namespace Modules\Authentication\Services;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cookie;

class TokenCookieService
{
    /**
     * Cookie names for different token types
     */
    const COOKIE_ACCESS_TOKEN = 'topdev_token';
    const COOKIE_REFRESH_TOKEN = 'topdev_refresh_token';
    const COOKIE_USER_ID = 'TDUID';
    const COOKIE_DEVICE_TOKEN = 'topdev-device-token-web';

    /**
     * Set access token cookie for multi-subdomain
     *
     * @param string $token
     * @param int $expiresInMinutes
     * @return void
     */
    public function setAccessTokenCookie(string $token, int $expiresInMinutes = 1440): void
    {
        Cookie::queue(Cookie::make(
            self::COOKIE_ACCESS_TOKEN,
            $token,
            $expiresInMinutes,
            '/',
            config('session.domain', '.topdev.vn'),
            config('session.secure', false),
            false // httpOnly = false for frontend access
        ));
    }

    /**
     * Set refresh token cookie (httpOnly for security)
     *
     * @param string $refreshToken
     * @param int $expiresInMinutes
     * @return void
     */
    public function setRefreshTokenCookie(string $refreshToken, int $expiresInMinutes = 525600): void
    {
        Cookie::queue(Cookie::make(
            self::COOKIE_REFRESH_TOKEN,
            $refreshToken,
            $expiresInMinutes,
            '/',
            config('session.domain', '.topdev.vn'),
            config('session.secure', false),
            true // httpOnly = true for security
        ));
    }

    /**
     * Set user ID cookie
     *
     * @param string $userId
     * @param int $expiresInMinutes
     * @return void
     */
    public function setUserIdCookie(string $userId, int $expiresInMinutes = 525600): void
    {
        Cookie::queue(Cookie::make(
            self::COOKIE_USER_ID,
            $userId,
            $expiresInMinutes,
            '/',
            config('session.domain', '.topdev.vn'),
            config('session.secure', false),
            false // httpOnly = false for frontend access
        ));
    }

    /**
     * Set device token cookie
     *
     * @param string $deviceToken
     * @param int $expiresInMinutes
     * @return void
     */
    public function setDeviceTokenCookie(string $deviceToken, int $expiresInMinutes = 525600): void
    {
        Cookie::queue(Cookie::make(
            self::COOKIE_DEVICE_TOKEN,
            $deviceToken,
            $expiresInMinutes,
            '/',
            config('session.domain', '.topdev.vn'),
            config('session.secure', false),
            false // httpOnly = false for frontend access
        ));
    }

    /**
     * Get access token from cookie
     *
     * @param Request $request
     * @return string|null
     */
    public function getAccessTokenFromCookie(Request $request): ?string
    {
        return $request->cookie(self::COOKIE_ACCESS_TOKEN);
    }

    /**
     * Get refresh token from cookie
     *
     * @param Request $request
     * @return string|null
     */
    public function getRefreshTokenFromCookie(Request $request): ?string
    {
        return $request->cookie(self::COOKIE_REFRESH_TOKEN);
    }

    /**
     * Get user ID from cookie
     *
     * @param Request $request
     * @return string|null
     */
    public function getUserIdFromCookie(Request $request): ?string
    {
        return $request->cookie(self::COOKIE_USER_ID);
    }

    /**
     * Get device token from cookie
     *
     * @param Request $request
     * @return string|null
     */
    public function getDeviceTokenFromCookie(Request $request): ?string
    {
        return $request->cookie(self::COOKIE_DEVICE_TOKEN);
    }

    /**
     * Clear all authentication cookies
     *
     * @return void
     */
    public function clearAllCookies(): void
    {
        $domain = config('session.domain', '.topdev.vn');
        
        Cookie::queue(Cookie::forget(self::COOKIE_ACCESS_TOKEN, '/', $domain));
        Cookie::queue(Cookie::forget(self::COOKIE_REFRESH_TOKEN, '/', $domain));
        Cookie::queue(Cookie::forget(self::COOKIE_USER_ID, '/', $domain));
        Cookie::queue(Cookie::forget(self::COOKIE_DEVICE_TOKEN, '/', $domain));
    }

    /**
     * Set all authentication cookies at once
     *
     * @param string $accessToken
     * @param string|null $refreshToken
     * @param string $userId
     * @param string|null $deviceToken
     * @return void
     */
    public function setAllAuthCookies(
        string $accessToken,
        ?string $refreshToken = null,
        string $userId = null,
        ?string $deviceToken = null
    ): void {
        $this->setAccessTokenCookie($accessToken);
        
        if ($refreshToken) {
            $this->setRefreshTokenCookie($refreshToken);
        }
        
        if ($userId) {
            $this->setUserIdCookie($userId);
        }
        
        if ($deviceToken) {
            $this->setDeviceTokenCookie($deviceToken);
        }
    }
}
