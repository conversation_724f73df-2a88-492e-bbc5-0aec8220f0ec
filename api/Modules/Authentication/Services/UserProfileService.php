<?php

namespace Modules\Authentication\Services;

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Modules\User\Entities\User;

class UserProfileService
{
    /**
     * Create user profile by user ID (match accounts logic)
     *
     * @param int $userId
     * @return mixed
     */
    public function createUserProfileByUserId(int $userId)
    {
        // Check if UserProfile model exists
        $userProfileClass = 'Modules\\User\\Entities\\UserProfile';
        if (!class_exists($userProfileClass)) {
            Log::warning("UserProfile model not found, skipping profile creation for user: {$userId}");
            return null;
        }

        $userProfile = $userProfileClass::where('user_id', $userId)->first();

        if ($userProfile) {
            return $userProfile;
        }

        try {
            return $userProfileClass::create([
                'user_id' => $userId,
                'skills' => (object)[],
                'experiences' => [],
                'educations' => [],
                'projects' => [],
                'languages' => [],
                'interests' => [],
                'references' => [],
                'activities' => [],
                'certificates' => [],
                'additionals' => [],
                'completed_sections' => [],
            ]);
        } catch (\Exception $ex) {
            Log::error("UserProfileService@createUserProfileByUserId: " . $ex->getMessage());
            return false;
        }
    }

    /**
     * Create user main CV (match accounts logic)
     *
     * @param int $userId
     * @param int|null $userProfileId
     * @return mixed
     */
    public function createUserMainCv(int $userId, ?int $userProfileId = null)
    {
        // Check if CV model exists
        $cvClass = 'Modules\\User\\Entities\\Cv';
        if (!class_exists($cvClass)) {
            Log::warning("CV model not found, skipping CV creation for user: {$userId}");
            return null;
        }

        try {
            // Check if main CV already exists
            $existingCv = $cvClass::where('user_id', $userId)
                ->where('is_main', true)
                ->first();

            if ($existingCv) {
                return $existingCv;
            }

            // Create main CV
            return $cvClass::create([
                'user_id' => $userId,
                'user_profile_id' => $userProfileId,
                'name' => 'CV chính',
                'is_main' => true,
                'is_public' => true,
                'status' => 'active',
            ]);
        } catch (\Exception $ex) {
            Log::error("UserProfileService@createUserMainCv: " . $ex->getMessage());
            return false;
        }
    }

    /**
     * Create search candidate entry (match accounts logic)
     *
     * @param int $userId
     * @return bool
     */
    public function createSearchCandidateByUserId(int $userId): bool
    {
        try {
            // Check if search_candidates table exists
            if (!DB::getSchemaBuilder()->hasTable('search_candidates')) {
                Log::warning("search_candidates table not found, skipping for user: {$userId}");
                return false;
            }

            // Check if entry already exists
            $exists = DB::table('search_candidates')
                ->where('user_id', $userId)
                ->exists();

            if ($exists) {
                return true;
            }

            // Create search candidate entry
            DB::table('search_candidates')->insert([
                'user_id' => $userId,
                'created_at' => now(),
                'updated_at' => now(),
            ]);

            return true;
        } catch (\Exception $ex) {
            Log::error("UserProfileService@createSearchCandidateByUserId: " . $ex->getMessage());
            return false;
        }
    }

    /**
     * Restore user if deleted (match accounts logic)
     *
     * @param User $user
     * @return void
     */
    public function restoreIfUserHasDeleted(User $user): void
    {
        if ($user->trashed()) {
            $user->restore();
            $user->wasRecentlyCreated = true;
        }

        // Create user profile
        $userProfile = $this->createUserProfileByUserId($user->id);

        // Create main CV
        if ($userProfile) {
            $this->createUserMainCv($user->id, $userProfile->id);
        }

        // Create search candidate entry
        $this->createSearchCandidateByUserId($user->id);
    }

    /**
     * Complete user setup after registration (match accounts logic)
     *
     * @param User $user
     * @return void
     */
    public function completeUserSetup(User $user): void
    {
        try {
            DB::transaction(function () use ($user) {
                // Create user profile
                $userProfile = $this->createUserProfileByUserId($user->id);

                // Create main CV
                if ($userProfile) {
                    $this->createUserMainCv($user->id, $userProfile->id);
                }

                // Create search candidate entry
                $this->createSearchCandidateByUserId($user->id);

                Log::info("User setup completed for user: {$user->id}");
            });
        } catch (\Exception $ex) {
            Log::error("UserProfileService@completeUserSetup: " . $ex->getMessage());
        }
    }

    /**
     * Add tracking UTM parameters (match accounts logic)
     *
     * @param \Illuminate\Http\Request $request
     * @param User $user
     * @return void
     */
    public function addTrackingUtm($request, User $user): void
    {
        try {
            $utmData = [];

            // Collect UTM parameters
            $utmParams = ['utm_source', 'utm_medium', 'utm_campaign', 'utm_term', 'utm_content'];
            foreach ($utmParams as $param) {
                if ($request->has($param)) {
                    $utmData[$param] = $request->get($param);
                }
            }

            // Store UTM data if available
            if (!empty($utmData)) {
                // Check if UserTracking model exists
                $trackingClass = 'Modules\\User\\Entities\\UserTracking';
                if (class_exists($trackingClass)) {
                    $trackingClass::updateOrCreate(
                        ['user_id' => $user->id],
                        array_merge($utmData, [
                            'ip_address' => $request->ip(),
                            'user_agent' => $request->userAgent(),
                            'referrer' => $request->header('referer'),
                        ])
                    );
                }

                Log::info("UTM tracking added for user: {$user->id}", $utmData);
            }
        } catch (\Exception $ex) {
            Log::error("UserProfileService@addTrackingUtm: " . $ex->getMessage());
        }
    }
}
