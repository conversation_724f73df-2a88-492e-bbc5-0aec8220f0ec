<?php

namespace Modules\Authentication\Services;

use Illuminate\Support\Facades\Log;
use Modules\Authentication\Support\CodeGenerator;
use Modules\VerificationCode\Entities\VerificationCode;

class VerificationCodeService
{
    /**
     * Generate and send verification code.
     *
     * @param string $email
     * @param string $type
     * @return VerificationCode
     */
    public function generateAndSend($email, $type = 'email_verification')
    {
        // Generate code
        $code = CodeGenerator::generate();
        
        // Create verification code record
        $verificationCode = VerificationCode::create([
            'email' => $email,
            'code' => $code,
            'type' => $type,
            'expires_at' => now()->addMinutes(config('authentication.verification_code.expires_minutes', 15)),
        ]);

        // Send code (implement your sending logic)
        $this->sendCode($email, $code, $type);

        Log::info('Verification code generated', [
            'email' => $email,
            'type' => $type,
            'code_id' => $verificationCode->id,
        ]);

        return $verificationCode;
    }

    /**
     * Verify a code.
     *
     * @param string $email
     * @param string $code
     * @param string $type
     * @return bool
     */
    public function verify($email, $code, $type = 'email_verification')
    {
        $verificationCode = VerificationCode::where('email', $email)
            ->where('code', $code)
            ->where('type', $type)
            ->where('expires_at', '>', now())
            ->where('used_at', null)
            ->first();

        if (!$verificationCode) {
            Log::warning('Invalid verification code attempt', [
                'email' => $email,
                'code' => $code,
                'type' => $type,
            ]);
            return false;
        }

        // Mark as used
        $verificationCode->update(['used_at' => now()]);

        Log::info('Verification code verified successfully', [
            'email' => $email,
            'type' => $type,
            'code_id' => $verificationCode->id,
        ]);

        return true;
    }

    /**
     * Send verification code.
     *
     * @param string $email
     * @param string $code
     * @param string $type
     * @return void
     */
    protected function sendCode($email, $code, $type)
    {
        // Implement your sending logic here
        // For example, dispatch a job to send email
        
        Log::info('Sending verification code', [
            'email' => $email,
            'code' => $code,
            'type' => $type,
        ]);
        
        // Example: dispatch(new SendVerificationCodeJob($email, $code, $type));
    }

    /**
     * Clean expired codes.
     *
     * @return int Number of deleted records
     */
    public function cleanExpiredCodes()
    {
        $deletedCount = VerificationCode::where('expires_at', '<', now())->delete();
        
        Log::info('Cleaned expired verification codes', [
            'deleted_count' => $deletedCount,
        ]);

        return $deletedCount;
    }
}
