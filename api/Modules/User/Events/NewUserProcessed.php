<?php

namespace Modules\User\Events;

use Illuminate\Queue\SerializesModels;
use Modules\User\Entities\User;

class NewUserProcessed
{
    use SerializesModels;

    /**
     * @var \Modules\User\Entities\User
     */
    private $user;

    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct(User $user, private $payload)
    {
        $this->user = $user;
    }

    /**
     * Get user model instance.
     *
     * @return \Modules\User\Entities\User
     */
    public function user()
    {
        return $this->user;
    }

    /**
     * @return array
     */
    public function payload()
    {
        return $this->payload;
    }
}
