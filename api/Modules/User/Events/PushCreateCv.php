<?php

namespace Modules\User\Events;

use Illuminate\Queue\SerializesModels;
use Illuminate\Foundation\Events\Dispatchable;
use Modules\User\Entities\User;

class PushCreateCv
{
    use Dispatchable, SerializesModels;

    /**
     * The user instance.
     *
     * @var User
     */
    public $user;

    /**
     * The device token for FCM notifications.
     *
     * @var string|null
     */
    public $device_token;

    /**
     * Create a new event instance.
     *
     * @param  User  $user
     * @param  string|null  $device_token
     * @return void
     */
    public function __construct(User $user, $device_token = null)
    {
        $this->user = $user;
        $this->device_token = $device_token;
    }
}
