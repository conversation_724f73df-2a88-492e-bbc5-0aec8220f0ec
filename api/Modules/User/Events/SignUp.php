<?php

namespace Modules\User\Events;

use Illuminate\Queue\SerializesModels;
use Illuminate\Foundation\Events\Dispatchable;
use Modules\User\Entities\User;

class SignUp
{
    use Dispatchable, SerializesModels;

    /**
     * The user instance.
     *
     * @var User
     */
    public $user;

    /**
     * Additional signup data.
     *
     * @var array
     */
    public $data;

    /**
     * Create a new event instance.
     *
     * @param User $user
     * @param array $data
     * @return void
     */
    public function __construct(User $user, $data = [])
    {
        $this->user = $user;
        $this->data = $data;
    }

    /**
     * Get user model instance.
     *
     * @return User
     */
    public function user()
    {
        return $this->user;
    }
}
