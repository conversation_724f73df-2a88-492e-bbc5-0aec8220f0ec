<?php

namespace Modules\User\Events;

use Illuminate\Queue\SerializesModels;
use Illuminate\Foundation\Events\Dispatchable;
use Modules\User\Entities\User;

class PushUpdateUserProfile
{
    use Dispatchable, SerializesModels;

    /**
     * The user instance.
     *
     * @var User
     */
    public $user;

    /**
     * The fields to update.
     *
     * @var array|null
     */
    public $arrFields;

    /**
     * The device token for FCM notifications.
     *
     * @var string|null
     */
    public $device_token;

    /**
     * Create a new event instance.
     *
     * @param  User  $user
     * @param  array|null  $arrFields
     * @param  string|null  $device_token
     * @return void
     */
    public function __construct(User $user, $arrFields = null, $device_token = null)
    {
        $this->user = $user;
        $this->arrFields = $arrFields;
        $this->device_token = $device_token;
    }
}
