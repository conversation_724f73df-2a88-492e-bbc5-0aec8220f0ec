<?php

namespace Modules\User\Listeners;

use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;
use Modules\User\Events\SignUp;

class SendEventToGA4GoogleAnalytics implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Handle the event.
     *
     * @param SignUp $event
     * @return void
     */
    public function handle(SignUp $event)
    {
        try {
            $user = $event->user;
            
            Log::info('SendEventToGA4GoogleAnalytics processing for user: ' . $user->email);
            
            // Send signup event to Google Analytics 4
            $this->sendToGA4($user);
            
        } catch (\Exception $e) {
            Log::error('SendEventToGA4GoogleAnalytics failed: ' . $e->getMessage(), [
                'user_id' => $event->user->id ?? null,
                'error' => $e->getMessage(),
            ]);
            
            throw $e;
        }
    }

    /**
     * Send signup event to Google Analytics 4.
     *
     * @param \Modules\User\Entities\User $user
     * @return void
     */
    protected function sendToGA4($user)
    {
        $ga4Config = config('services.ga4');
        
        if (empty($ga4Config)) {
            Log::warning('GA4 configuration not found for signup event');
            return;
        }

        $eventData = [
            'name' => 'sign_up',
            'params' => [
                'user_id' => $user->id,
                'user_type' => $user->type,
                'method' => 'email', // or social provider if applicable
                'timestamp' => now()->timestamp,
            ],
        ];

        // Here you would implement the actual GA4 API call
        // For now, we'll just log it
        Log::info('GA4 Signup Event Data', $eventData);
        
        // Example implementation:
        // $this->dispatchGA4Event($eventData);
    }

    /**
     * Handle a job failure.
     *
     * @param \Throwable $exception
     * @return void
     */
    public function failed(\Throwable $exception)
    {
        Log::error('SendEventToGA4GoogleAnalytics job failed', [
            'error' => $exception->getMessage(),
        ]);
    }
}
