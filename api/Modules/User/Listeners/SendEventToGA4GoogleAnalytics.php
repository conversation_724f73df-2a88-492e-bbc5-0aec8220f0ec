<?php

namespace Modules\User\Listeners;

use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Support\Facades\Cookie;
use Illuminate\Support\Facades\Log;
use Modules\Authentication\Contracts\GA4EventInterface;
use Modules\Authentication\Events\AuthenticationLogCreated;

class SendEventToGA4GoogleAnalytics
{
    /**
     * Handle the event.
     *
     * @param  GA4EventInterface  $event
     *
     * @return void
     * @throws GuzzleException
     */
    public function handle(GA4EventInterface $event)
    {
        if (!$this->isEventSendable($event)) {
            Log::info('[SendEventToGA4GoogleAnalytics] Event not sendable → Skip.');
            return;
        }

        foreach (config('services.ga4', []) as $ga4) {
            $jsonData = [
                'client_id' => $this->getGaClientId(),
                'events' => [
                    $event->getGa4EventData($ga4['measurement_id']),
                ],
            ];

            try {
                $client = new \GuzzleHttp\Client();
                $response = $client->post(
                    'https://www.google-analytics.com/mp/collect?api_secret=' . $ga4['api_secret'] . '&measurement_id=' . $ga4['measurement_id'],
                    [
                        'json' => $jsonData,
                        'timeout' => 5,
                    ]
                );

                Log::info(
                    '[SendEventToGA4GoogleAnalytics] Send event to GA4 success',
                    ['id' => $ga4['measurement_id'], 'data' => $jsonData, 'status' => $response->getStatusCode()]
                );

            } catch (\Exception $e) {
                Log::error(
                    '[SendEventToGA4GoogleAnalytics] Failed to send event to GA4',
                    ['id' => $ga4['measurement_id'], 'data' => $jsonData, 'error' => $e->getMessage()]
                );
            }
        }
    }

    /**
     * Check if event should be sent to GA4.
     *
     * @param GA4EventInterface $event
     * @return bool
     */
    protected function isEventSendable(GA4EventInterface $event)
    {
        if ($event instanceof AuthenticationLogCreated && $event->authenlog && !in_array($event->authenlog->type_login, ['web', 'mobile'])) {
            Log::info('[SendEventToGA4GoogleAnalytics] AuthenLogCreated type is not web or mobile');
            return false;
        }

        return true;
    }

    /**
     * Get Google Analytics client ID from cookie.
     *
     * @return string
     */
    protected function getGaClientId()
    {
        $gaClientId = Cookie::get('_ga');

        if (!$gaClientId) {
            return 'topdev_system';
        }

        $clientId = substr($gaClientId, 6);

        if (!$clientId) {
            return 'topdev_system';
        }

        return $clientId;
    }
}
