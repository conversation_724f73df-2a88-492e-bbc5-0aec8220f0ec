<?php

namespace Modules\User\Listeners;

use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

class PublishEvent implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Handle the event.
     *
     * @param  object  $event
     * @return void
     */
    public function handle($event)
    {
        try {
            // Get event data
            $eventData = $this->extractEventData($event);
            
            Log::info('Publishing event to queue', [
                'event_type' => get_class($event),
                'event_data' => $eventData,
            ]);

            // Publish to RabbitMQ or other queue system
            $this->publishToQueue($eventData);
            
        } catch (\Exception $e) {
            Log::error('Failed to publish event: ' . $e->getMessage(), [
                'event_type' => get_class($event),
                'error' => $e->getMessage(),
            ]);
            
            throw $e;
        }
    }

    /**
     * Extract data from event.
     *
     * @param  object  $event
     * @return array
     */
    protected function extractEventData($event)
    {
        $data = [
            'event_type' => get_class($event),
            'timestamp' => now()->toISOString(),
        ];

        // Extract user data if available
        if (property_exists($event, 'user') && $event->user) {
            $data['user'] = [
                'id' => $event->user->id,
                'email' => $event->user->email,
                'type' => $event->user->type,
                'display_name' => $event->user->display_name,
            ];
        }

        // Extract additional fields based on event type
        if (property_exists($event, 'arrFields')) {
            $data['fields'] = $event->arrFields;
        }

        if (property_exists($event, 'device_token')) {
            $data['device_token'] = $event->device_token;
        }

        return $data;
    }

    /**
     * Publish event data to queue system.
     *
     * @param  array  $eventData
     * @return void
     */
    protected function publishToQueue($eventData)
    {
        // This would integrate with your queue system (RabbitMQ, Redis, etc.)
        // For now, we'll just log it
        
        $queueName = $this->getQueueName($eventData['event_type']);
        
        Log::info('Publishing to queue: ' . $queueName, $eventData);
        
        // Example: QueueHelper::createQueueRabbitmq($queueName, $eventData);
        // You would implement the actual queue publishing logic here
    }

    /**
     * Get queue name based on event type.
     *
     * @param  string  $eventType
     * @return string
     */
    protected function getQueueName($eventType)
    {
        $queueMap = [
            'Modules\User\Events\NewUserProcessed' => 'user.new_user_processed',
            'Modules\User\Events\UserHasRecentlyUpdateProfile' => 'user.profile_updated',
            'Modules\User\Events\PushCreateCv' => 'user.push_create_cv',
            'Modules\User\Events\PushUpdateUserProfile' => 'user.push_update_profile',
        ];

        return $queueMap[$eventType] ?? 'default.events';
    }

    /**
     * Handle a job failure.
     *
     * @param  \Throwable  $exception
     * @return void
     */
    public function failed(\Throwable $exception)
    {
        Log::error('PublishEvent listener failed', [
            'error' => $exception->getMessage(),
        ]);
    }
}
