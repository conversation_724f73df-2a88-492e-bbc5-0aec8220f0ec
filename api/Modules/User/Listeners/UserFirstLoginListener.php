<?php

namespace Modules\User\Listeners;

use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;
use Modules\Authentication\Events\UserFirstLoginFromWeb;
use Modules\User\Events\NewUserProcessed;

class UserFirstLoginListener implements ShouldQueue
{
    use InteractsWithQueue;

    /**
     * Handle the event.
     *
     * @param  UserFirstLoginFromWeb  $event
     * @return void
     */
    public function handle(UserFirstLoginFromWeb $event)
    {
        try {
            $authenlog = $event->authenlog;
            
            if (!empty($authenlog->authenticatable)) {
                $user = $authenlog->authenticatable;
                
                Log::info('UserFirstLoginListener processing for user: ' . $user->email);
                
                // Fire NewUserProcessed event
                event(new NewUserProcessed($user));
                
                // Additional first login processing
                $this->processFirstLogin($user);
                
            }
        } catch (\Exception $e) {
            Log::error('UserFirstLoginListener failed: ' . $e->getMessage(), [
                'authenlog_id' => $event->authenlog->id ?? null,
                'error' => $e->getMessage(),
            ]);
            
            throw $e;
        }
    }

    /**
     * Process additional first login logic.
     *
     * @param  \Modules\User\Entities\User  $user
     * @return void
     */
    protected function processFirstLogin($user)
    {
        // Send welcome email if needed
        if ($user->type === 'employer' && !$user->hasApprovedAccount()) {
            // Send email verification for employers
            $user->sendEmailVerificationNotification();
        } else {
            // Send welcome notification for other users
            $this->sendWelcomeNotification($user);
        }

        // Track first login analytics
        $this->trackFirstLoginAnalytics($user);
    }

    /**
     * Send welcome notification to user.
     *
     * @param  \Modules\User\Entities\User  $user
     * @return void
     */
    protected function sendWelcomeNotification($user)
    {
        try {
            // You can implement welcome notification logic here
            Log::info('Sending welcome notification to user: ' . $user->email);
            
            // Example: $user->notify(new WelcomeNotification());
            
        } catch (\Exception $e) {
            Log::error('Failed to send welcome notification: ' . $e->getMessage());
        }
    }

    /**
     * Track first login analytics.
     *
     * @param  \Modules\User\Entities\User  $user
     * @return void
     */
    protected function trackFirstLoginAnalytics($user)
    {
        try {
            // Track first login event
            Log::info('Tracking first login analytics for user: ' . $user->id);
            
            // You can dispatch analytics job here
            // dispatch(new DispatchAuthAnalyticsJob($user, 'first_login'));
            
        } catch (\Exception $e) {
            Log::error('Failed to track first login analytics: ' . $e->getMessage());
        }
    }

    /**
     * Handle a job failure.
     *
     * @param  \Throwable  $exception
     * @return void
     */
    public function failed(\Throwable $exception)
    {
        Log::error('UserFirstLoginListener job failed', [
            'error' => $exception->getMessage(),
        ]);
    }
}
