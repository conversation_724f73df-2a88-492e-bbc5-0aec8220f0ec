<?php

namespace Modules\User\Entities;

use App\Traits\HasCustomRelations;
use App\Traits\UseUuid;
use Arcanedev\LaravelNotes\Traits\HasManyNotes;
use Carbon\Carbon;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Laravel\Sanctum\HasApiTokens;
use Laravel\Scout\Searchable;
use Modules\Activity\Contracts\ViewableContract;
use Modules\Activity\Traits\HasTriggerActionEngie;
use Modules\Activity\Traits\LogsActivity;
use Modules\Admin\Form\Field;
use Modules\Authentication\Entities\AuthenticationLog;
use Modules\Company\Entities\Company;
use Modules\Company\Repositories\Contracts\CompanyRepositoryInterface;
use Modules\File\Entities\Media;
use Modules\File\Traits\HasFile;
use Modules\File\Transformers\AssetsResourceCollection;
use Modules\Job\Entities\Candidate;
use Modules\Job\Entities\Job;
use Modules\Job\Repositories\Contracts\CandidateRepositoryInterface;
use Modules\Job\Repositories\Contracts\JobRepositoryInterface;
use Modules\Meta\Traits\Metable;
use Modules\Order\Entities\Cart;
use Modules\Order\Entities\Order;
use Modules\Taxonomy\Entities\Taxonomy;
use Modules\SearchCandidate\Entities\SearchCandidate;
use Modules\Taxonomy\Traits\Taxoable;
use Modules\User\Contracts\AmsAuthenticatable;
use Modules\User\Entities\Contracts\MustApproveAccount as MustApproveAccountContract;
use Modules\User\Jobs\ParseCvUploadProcess;
use Modules\User\Jobs\SyncSearchCandidateProcess;
use Modules\User\Entities\Traits\MustApproveAccount;
use Modules\User\Traits\HasAnnouncements;
use Modules\User\Traits\HasBlacklist;
use Modules\VietnamArea\Traits\Addressable;
use Overtrue\LaravelFollow\Traits\CanFollow;
use Overtrue\LaravelFollow\Traits\CanSubscribe;
use OwenIt\Auditing\Contracts\Auditable;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\MediaCollections\File;
use Symfony\Component\HttpFoundation\File\UploadedFile;

/**
 * Modules\User\Entities\User.
 *
 * @property int $id
 * @property string $uuid
 * @property string|null $firstname
 * @property string|null $lastname
 * @property string|null $display_name
 * @property string $username
 * @property string $email
 * @property string|null $phone
 * @property string|null $gender male|female|homosexual
 * @property string|null $birthday
 * @property string|null $password
 * @property string|null $description
 * @property string|null $type using resume|employer|partner|others
 * @property string|null $remember_token
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property int|null $company_id
 * @property string|null $approved_at
 * @property string|null $email_verified_at
 * @property string|null $position
 * @property string|null $freeze_at
 * @property int $allow_share_cv
 * @property-read \Illuminate\Database\Eloquent\Collection|\Modules\Activity\Entities\Activity[] $activities
 * @property-read int|null $activities_count
 * @property \Illuminate\Database\Eloquent\Collection|\Modules\VietnamArea\Entities\Address[] $addresses
 * @property-read int|null $addresses_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\Modules\User\Entities\Announcement[] $announcedJobs
 * @property-read int|null $announced_jobs_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\Modules\User\Entities\Announcement[] $announcements
 * @property-read int|null $announcements_count
 * @property-read \Illuminate\Database\Eloquent\Collection|Job[] $appliedJobs
 * @property-read int|null $applied_jobs_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\OwenIt\Auditing\Models\Audit[] $audits
 * @property-read int|null $audits_count
 * @property-read \Illuminate\Database\Eloquent\Collection|Job[] $authoredJobs
 * @property-read int|null $authored_jobs_count
 * @property-read \Illuminate\Database\Eloquent\Collection|Candidate[] $candidates
 * @property-read int|null $candidates_count
 * @property-read Company|null $company
 * @property-read array $address_locality_array
 * @property-read array $address_locality
 * @property-read string $address_locality_list
 * @property-read array $address_region_array
 * @property-read array $address_region
 * @property-read array $address_region_ids
 * @property-read MorphMany $address_region_list
 * @property-read MorphMany $address_short_region_list
 * @property \Modules\User\Entities\url $avatar
 * @property-read string $avatar_url
 * @property-read bool $changeable_password
 * @property-read array $collection_addresses
 * @property-read array $full_addresses
 * @property string $full_name
 * @property-read mixed $image_logo_url
 * @property-read string $list_id_companies
 * @property-read \Collection $meta_collect
 * @property-read string $profile
 * @property-read string $ready_to_apply
 * @property-read string $short_addresses
 * @property array $terms
 * @property-read int $years_of_exp_upgraded
 * @property-read \Illuminate\Database\Eloquent\Collection|Media[] $media
 * @property-read int|null $media_count
 * @property \Illuminate\Database\Eloquent\Collection|\Plank\Metable\Meta[] $meta
 * @property-read int|null $meta_count
 * @property-read Candidate|null $newestCandidate
 * @property-read \Illuminate\Database\Eloquent\Collection|\Arcanedev\LaravelNotes\Models\Note[] $notes
 * @property-read int|null $notes_count
 * @property-read \Illuminate\Notifications\DatabaseNotificationCollection|\Illuminate\Notifications\DatabaseNotification[] $notifications
 * @property-read int|null $notifications_count
 * @property-read \Illuminate\Database\Eloquent\Collection|UserKeyword[] $searchHistory
 * @property-read int|null $search_history_count
 * @property-write mixed $force_meta
 * @property-read \Illuminate\Database\Eloquent\Collection|Taxonomy[] $taxonomies
 * @property-read int|null $taxonomies_count
 * @property-read \Modules\User\Entities\UserProfile|null $userProfile
 * @property-read \Modules\Authentication\Entities\AuthenticationLog[]|\Illuminate\Database\Eloquent\Collection $authentications
 * @method static Builder|User approved()
 * @method static Builder|User doesntHaveApply()
 * @method static Builder|User employers()
 * @method static Builder|User haveApply()
 * @method static Builder|User newModelQuery()
 * @method static Builder|User newQuery()
 * @method static \Illuminate\Database\Query\Builder|User onlyTrashed()
 * @method static Builder|User orderByMeta(string $key, string $direction = 'asc', bool $strict = false)
 * @method static Builder|User orderByMetaNumeric(string $key, string $direction = 'asc', bool $strict = false)
 * @method static Builder|User query()
 * @method static Builder|User quickSearch($keyword)
 * @method static Builder|User resumes()
 * @method static Builder|User whereAllowShareCv($value)
 * @method static Builder|User whereApprovedAt($value)
 * @method static Builder|User whereBirthday($value)
 * @method static Builder|User whereCompanyId($value)
 * @method static Builder|User whereCreatedAt($value)
 * @method static Builder|User whereDeletedAt($value)
 * @method static Builder|User whereDescription($value)
 * @method static Builder|User whereDisplayName($value)
 * @method static Builder|User whereDoesntHaveMeta($key)
 * @method static Builder|User whereEmail($value)
 * @method static Builder|User whereEmailVerifiedAt($value)
 * @method static Builder|User whereFirstname($value)
 * @method static Builder|User whereFreezeAt($value)
 * @method static Builder|User whereGender($value)
 * @method static Builder|User whereHasMeta($key)
 * @method static Builder|User whereHasMetaKeys(array $keys)
 * @method static Builder|User whereId($value)
 * @method static Builder|User whereLastname($value)
 * @method static Builder|User whereMeta(string $key, $operator, $value = null)
 * @method static Builder|User whereMetaIn(string $key, array $values)
 * @method static Builder|User whereMetaNumeric(string $key, string $operator, $value)
 * @method static Builder|User wherePassword($value)
 * @method static Builder|User wherePhone($value)
 * @method static Builder|User wherePosition($value)
 * @method static Builder|User whereRememberToken($value)
 * @method static Builder|User whereType($value)
 * @method static Builder|User whereUpdatedAt($value)
 * @method static Builder|User whereUsername($value)
 * @method static Builder|User whereUuid($value)
 * @method static Builder|User withAllArea($areas = [], $type = null)
 * @method static Builder|User withAllTerms($terms, ?string $taxonomy = null)
 * @method static Builder|User withAnyArea($areas = [], $type = null)
 * @method static Builder|User withAnyTerms($terms, ?string $taxonomy = null)
 * @method static Builder|User withTermsName($keyword)
 * @method static \Illuminate\Database\Query\Builder|User withTrashed()
 * @method static \Illuminate\Database\Query\Builder|User withoutTrashed()
 * @mixin \Eloquent
 * @property-read \Illuminate\Database\Eloquent\Collection|Company[] $blockedCompanies
 * @property-read int|null $blocked_companies_count
 * @property-read \Illuminate\Database\Eloquent\Collection|MyResume[] $cvBuilders
 * @property-read int|null $cv_builders_count
 * @property-read UserMainCv|null $mainCv
 * @property-read \Illuminate\Database\Eloquent\Collection|UserOTWStatusLog[] $openToWorkLogs
 * @property-read int|null $open_to_work_logs_count
 * @property-read int|bool $willing_to_work
 * @property-read \Illuminate\Database\Eloquent\Collection|\Modules\Taxonomy\Entities\Taxonomy $status_works
 */
class User extends AmsAuthenticatable implements HasMedia, MustApproveAccountContract, Auditable
{
    use UseUuid;
    use \OwenIt\Auditing\Auditable;
    use LogsActivity;
    use SoftDeletes;
    use Addressable;
    use HasFile;
    use Taxoable;
    use HasManyNotes;
    use Metable;
    use CanSubscribe;
    use CanFollow;
    use Notifiable;
    use Searchable;
    use MustApproveAccount;
    use HasCustomRelations;
    use HasAnnouncements;
    use HasTriggerActionEngie;
    use HasBlacklist;
    use HasApiTokens;

    public const RESUME_TYPE = 'resume';
    public const EMPLOYER_TYPE = 'employer';
    public const PARTNER_TYPE = 'partner';
    public const OTHER_TYPE = 'other';
    public const ADMINISTRATOR_TYPE = 'administrator';
    public const ALLOW_SHARE_CV = 1;
    public const GENDERS = ['Male', 'Female', 'Homosexual'];

    /**
     * @inheritdoc
     */
    protected $table = 'users';

    /**
     * @inheritdoc
     */
    public array $files = [
        'files_cv', 'files_cv_null', 'avatar', 'files_cvbuilder',
    ];

    /**
     * The name of the "approved at" column.
     *
     * @var string
     */
    public const APPROVED_AT = 'approved_at';

    /**
     * The attributes that should be append to native types.
     *
     * @var array
     */
    public $scopeMeta = [
        'social_networks' => [],
        'source' => null,
        'skype' => null,
        'languages' => [],
        'educations' => [],
        'projects' => [],
        'cover_letter' => null,
        'years_of_exp' => null,
        'years_of_edu' => null,
        'experiences' => [],
        'certificates' => [],
        'expected_salary' => null,
        'seeking_candidate' => null,
        'recent_position' => null,
        'willing_to_work' => false,
        'auto_apply' => false,
    ];

    /**
     * The attributes that should be append to native types.
     *
     * @var array
     */
    protected array $arrayValues = [
        'social_networks', 'languages', 'educations', 'projects', 'certificates', 'experiences',
    ];

    /**
     * The attributes that should be append to native types.
     *
     * @var array
     */
    public array $scopeTerm = [
        'skills', 'extra_skills', 'salary_range', 'status_works', 'bottom_cv_questions',
    ];

    /**
     * @inheritdoc
     */
    protected $fillable = [
        'id', 'email', 'password', 'username', 'display_name', 'full_name', 'phone', 'company_id', 'type', 'salary_range', 'status_works', 'bottom_cv_questions',
        'files_cv', 'files_cv_null', 'files_cvbuilder', 'avatar', 'gender', 'cover_letter', 'skills', 'addresses', 'birthday', 'source', 'extra_skills', 'auto_apply',
        'willing_to_work', 'recent_position', 'social_networks', 'languages', 'educations', 'projects', 'experiences', 'seeking_candidate', 'remember_token',
        'email_verified_at', 'freeze_at',
    ];

    /**
     * @inheritdoc
     */
    protected $hidden = [
        'password', 'remember_token',
    ];

    /**
     * @inheritdoc
     */
    protected $appends = [];

    /**
     * @inheritdoc
     */
    protected $touches = ['candidates'];

    /**
     * @return void
     */
    public function __construct($attributes = [])
    {
        parent::__construct($attributes);
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    protected static function boot()
    {
        parent::boot();
        static::creating(function ($user) {
            if (empty($user->username)) {
                $user->username = $user->generateUsername(
                    strstr((string) $user->email, '@', true)
                );
            }
        });
    }

    /**
     * Register media for user.
     *
     * @return void
     */
    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('avatar')
            ->acceptsFile(fn(File $file) => $file->mimeType === 'image/jpeg'
                || $file->mimeType === 'image/png')
            ->singleFile();
    }

    /**
     * Get type for the model.
     *
     * @return array
     */
    public static function getUserTypeDescription()
    {
        return [
            static::RESUME_TYPE => 'Resume',
            static::EMPLOYER_TYPE => 'Employer',
            static::PARTNER_TYPE => 'Partner',
            static::OTHER_TYPE => 'Other',
            static::ADMINISTRATOR_TYPE => 'Administrator',
        ];
    }

    /**
     * Get link profile user.
     *
     * @return string
     */
    public function getProfileAttribute()
    {
        return url('/admin/users/' . $this->id);
    }

    /**
     * Lấy tên đầy đủ của user.
     *
     * @return string
     */
    public function getFullNameAttribute()
    {
        return $this->display_name;
    }

    public function setFullNameAttribute($value)
    {
        $this->display_name = $value;

        return $this;
    }

    /**
     * Lấy ra media sau cùng.
     *
     * @return array
     */
    public function getLatestMedia(string $collectionName = 'default', array $filters = [])
    {
        $media = $this->getMedia($collectionName, $filters);

        return $media->last();
    }

    /**
     * Get the route key for the model.
     *
     * @return string
     */
    public function getRouteKeyName()
    {
        return 'id';
    }

    /**
     * Get the index name for the model.
     *
     * @return string
     */
    public function searchableAs()
    {
        return 'users_ams_v1';
    }

    /**
     * quick search.
     *
     * @return Illuminate\Database\Eloquent\Builder
     */
    public function scopeQuickSearch(Builder $builder, $keyword)
    {
        return $builder->where('display_name', 'like', '%' . $keyword . '%')
            ->orWhere('phone', $keyword)
            ->orWhere('email', $keyword)
            ->orWhere('users.id', $keyword);
    }

    /**
     * Get the value used to index the model.
     *
     * @return mixed
     */
    public function getScoutKey()
    {
        return $this->id;
    }

    /**
     * Get the company current.
     *
     * @return \Illuminate\Database\Eloquent\Relations\belongsTo
     */
    public function company()
    {
        return $this->belongsTo(Company::class, 'company_id')
            ->withoutGlobalScopes()
            ->withDefault();
    }

    /**
     * Get the all companies of user.
     *
     * @return Collection
     */
    public function companies()
    {
        if (empty($this->company)) {
            return collect();
        }

        return collect([$this->company]);
    }

    /**
     * Get all job applied.
     *
     * @return string
     */
    public function appliedJobs()
    {
        return $this->belongsToMany(Job::class, Candidate::class, 'resume_id', 'job_id')
            ->withPivot(['created_at', 'updated_at', 'status', 'creator_id'])
            ->wherePivot('deleted_at', null);
    }

    /**
     * Get all job authored.
     *
     * @return string
     */
    public function authoredJobs()
    {
        return $this->hasMany(Job::class, 'creator_id');
    }

    public function applyGroups()
    {
        return $this->candidates->groupBy('group');
    }

    /**
     * Get all job followed.
     *
     * @return string
     */
    public function followedJobs($time = null)
    {
        if (empty($time)) {
            return $this->followings(Job::class)
                ->wherePivot('deleted_at', null);
        }

        return $this->followings(Job::class)
            ->wherePivot('deleted_at', null)
            ->wherePivot('created_at', '>=', $time)->get();
    }

    public function clickApply($time = null)
    {
        $applied = $this->candidates->pluck('job_id');

        $clicked = $this->viewedJobs('apply-now')
            ->wherePivotNotIn('viewable_id', $applied);

        if (empty($time)) {
            return $clicked;
        }

        return $clicked->wherePivot('viewed_at', '>=', $time)->get();
    }

    /**
     * Get all job viewed.
     *
     * @return mixed
     */
    public function viewedJobs($collection = 'view')
    {
        $class = Job::class;
        $table = config('eloquent-viewable.models.view.table_name');
        $foreignKey = 'user_id';
        $targetTable = (new Job)->getTable();
        $tablePrefixedForeignKey = app('db.connection')->getQueryGrammar()->wrap(\sprintf('pivot_viewables.%s', $foreignKey));
        $eachOtherKey = app('db.connection')->getQueryGrammar()->wrap('pivot_each_other');

        return $this->morphedByMany($class, 'viewable', $table)
            ->where($table . '.collection', $collection)
            ->addSelect("{$targetTable}.*", DB::raw("(CASE WHEN {$tablePrefixedForeignKey} IS NOT NULL THEN 1 ELSE 0 END) as {$eachOtherKey}"))
            ->leftJoin("{$table} as pivot_viewables", function ($join) use ($table, $class, $foreignKey) {
                $join->on('pivot_viewables.viewable_type', '=', DB::raw(\addcslashes("'{$class}'", '\\')))
                    ->on('pivot_viewables.viewable_id', '=', "{$table}.{$foreignKey}")
                    ->on("pivot_viewables.{$foreignKey}", '=', "{$table}.viewable_id");
            })
            ->withPivot('viewed_at');
    }

    /**
     * Get all company followed.
     *
     * @return string
     */
    public function followedCompanies()
    {
        return $this->followings(Company::class);
    }

    /**
     * Get newest candidate.
     *
     * @return string
     */
    public function newestCandidate()
    {
        return $this->hasOne(Candidate::class, 'resume_id', 'id');
    }

    /**
     * Get candidates.
     *
     * @return string
     */
    public function candidates()
    {
        return $this->hasMany(Candidate::class, 'resume_id', 'id');
    }

    /**
     * Get email unsubscribes
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function emailUnsubscribes()
    {
        return $this->belongsToMany(EmailCategory::class, 'email_unsubscribe', 'user_id', 'email_category_id')
            ->withTimestamps();
    }

    /**
     * Get media cv latest.
     *
     * @return Media
     */
    public function latestAttachment()
    {
        return $this->files_cv->merge($this->files_cvbuilder)
            ->sortByDesc('created_at')
            ->first();
    }

    /**
     * Get list id companies.
     *
     * @return string
     */
    public function getListIdCompaniesAttribute()
    {
        return $this->companies()->pluck('id')->toArray();
    }

    /**
     * Upgrade year for exp resumes.
     *
     * @return int
     */
    public function getYearsOfExpUpgradedAttribute()
    {
        return $this->created_at->diffInYears() + (int) $this->years_of_exp;
    }

    /**
     * Label for ready new job in resumes.
     *
     * @return string
     */
    public function getReadyToApplyAttribute()
    {
        $latest_applied = $this->appliedJobs->last();

        if (!empty($latest_applied)) {
            $time_ready_apply = $latest_applied->created_at->addMonth();

            if ($time_ready_apply->isFuture()) {
                return 'Ready';
            }
        }
    }

    public function getImageLogoUrlAttribute()
    {
        return $this->company->image_logo_url;
    }

    /**
     * Set password by bcrypt.
     *
     * @return void
     */
    public function setPasswordAttribute($value, $crypt = true)
    {
        if (empty($value)) {
            return;
        }

        if ($crypt) {
            $this->attributes['password'] = bcrypt($value);
        } else {
            $this->attributes['password'] = $value;
        }
    }

    /**
     * Generate unique username.
     *
     * @return string
     */
    public function generateUsername($origin)
    {
        return Str::slug($origin) . '_' . now()->timestamp;
    }

    /**
     *  Attribute check if changeable password.
     *
     * @return bool
     */
    public function getChangeablePasswordAttribute()
    {
        return !empty($this->password);
    }

    /**
     *  Scope employers.
     *
     * @return Illuminate\Database\Eloquent\Builder
     */
    public function scopeEmployers(Builder $query)
    {
        return $query->where('type', self::EMPLOYER_TYPE);
    }

    /**
     *  Scope resumes.
     *
     * @return Illuminate\Database\Eloquent\Builder
     */
    public function scopeResumes(Builder $query)
    {
        return $query->where('type', self::RESUME_TYPE);
    }

    /**
     *  Scope user had applied via topdev.
     *
     * @return Illuminate\Database\Eloquent\Builder
     */
    public function scopeHaveApply(Builder $query)
    {
        return $query->has('candidates');
    }

    /**
     *  Scope user doesnt have applied via topdev.
     *
     * @return Illuminate\Database\Eloquent\Builder
     */
    public function scopeDoesntHaveApply(Builder $query)
    {
        return $query->doesntHave('candidates');
    }

    /**
     * update activities on elasticsearch.
     */
    public function activityTracking(ViewableContract $viewable = null)
    {
    }

    /**
     * Sreach source form resume.
     *
     * @return array
     */
    public static function sources()
    {
        return [
            'TOPDEV' => 'TopDev',
            'APPLANCER' => 'Applancer',
            'SALARY' => 'Salary',
            'SLIMSHARE' => 'Slimshare',
            'EVENT' => 'Event',
            'QUIZ_DATA' => 'Quiz Data',
            'RESEARCH' => 'Research',
            'VIECLAMDAYROI' => 'Vieclamdayroi',
            'LINKEDIN' => 'LinkedIn',
        ];
    }

    /**
     * Attribute avatar.
     *
     * @return string
     */
    public function getAvatarUrlAttribute()
    {
        return $this->getFirstMediaUrl('avatar') ?? null;
    }

    /**
     * Get avatar.
     *
     * @return url
     */
    public function getAvatarAttribute()
    {
        return $this->getMedia('avatar')
            ->mapWithKeys(fn($media, $key) => [$media->getKey() => $media->getFullUrl()])
            ->toArray();
    }

    /**
     * Set avatar.
     *
     * @return void
     */
    public function setAvatarAttribute($values)
    {
        $key = 'avatar';

        $flag = Field::FILE_DELETE_FLAG;

        if ($flag == request()->get($key)) {
            return $this->deleteMedia(request()->get($flag));
        }

        collect($values)->each(function ($file, $index) use ($key) {
            $file_name = file_name_random($file);

            if (filter_var($file, FILTER_VALIDATE_URL)) {
            } elseif ($file instanceof UploadedFile) {
                $this->addMedia($file)->usingFileName($file_name)->toMediaCollection($key);
            } elseif (is_string($file)) {
                $this->addMediaFromDisk($file)
                    ->usingFileName($file_name)
                    ->preservingOriginal()
                    ->toMediaCollection($key);
            }
        });
    }

    /**
     * Get black list companies user.
     *
     * @return string
     */
    public function blacklistCompanies()
    {
        return $this->blacklists(Company::class);
    }

    // attribute actions when return cv
    public static function getFeatures()
    {
        return [
            'edit' => false,
            'delete' => true,
            'view' => true,
            'download' => true,
            'duplicate' => false,
            'apply' => true,
        ];
    }

    public function getAttachedResume()
    {
        $mediaCollection = $this->media()->whereIn('collection_name', ['files_cv', 'UploadFromCvBuilder', 'UploadFromTopdev', 'UploadFromMobile', 'UploadFromApplyToAll'])->orderBy('created_at', 'DESC')->get();
        $mediaCollection = collect($mediaCollection)->map(function ($media) {
            $features = self::getFeatures();

            $candidates = app(CandidateRepositoryInterface::class)->searchOnElasticsearch(
                null,
                [
                    'resume_id' => $media->model_id,
                    'media_id' => $media->id,
                    'page_size' => 10000,
                ]
            );

            $data = collect($candidates['hits']['hits'] ?? [])->map(function ($candidate) use (&$features) {
                $features['edit'] = false;
                $features['delete'] = false;
                $features['duplicate'] = true;

                if (isset($candidate['_source']['job']['id']) && !empty($candidate['_source']['job']['id'])) {
                    $job = app(JobRepositoryInterface::class)->searchOnElasticsearch(
                        null,
                        [
                            'ids' => $candidate['_source']['job']['id'],
                            'page_size' => 1,
                        ]
                    );
                }

                $job = (isset($job['hits']['hits'][0]) ?
                    collect($job['hits']['hits'][0]['_source'])->only('id', 'title', 'company', 'owned_id')->all() : null);

                $company = app(CompanyRepositoryInterface::class)->searchOnElasticsearch(
                    null,
                    [
                        'ids' => $job['owned_id'] ?? null,
                        'page_size' => 1,
                    ]
                );

                $company = (isset($company['hits']['hits'][0]) ?
                    collect($company['hits']['hits'][0]['_source'])->only('slug', 'image_logo', 'display_name')->all() : null);

                $data = [
                    'id' => $candidate['_source']['id'],
                    'applied_at' => strtotime(Carbon::createFromFormat('Y-m-d H:i:s', $candidate['_source']['created_at'])->format('H:i:s d-m-Y')),
                    'job_id' => $job['id'] ?? null,
                    'job_title' => $job['title'] ?? null,
                    'company_id' => $job['owned_id'] ?? null,
                    'company_slug' => $company['slug'] ?? null,
                    'company_image_logo' => $company['image_logo'] ?? null,
                    'company_name' => $company['display_name'] ?? null,
                ];

                return $data;
            })->toArray();

            return [
                'features' => $features,
                'id' => (string) $media->getKey(),
                'source' => 'Uploaded from computer',
                'name' => $media->name,
                'created_at' => strtotime(Carbon::createFromFormat('Y-m-d H:i:s', $media->created_at)->format('H:i:s d-m-Y')),
                // 'updated_at' => strtotime(Carbon::createFromFormat('Y-m-d H:i:s', $media->updated_at)->format('H:i:s d-m-Y')),
                'updated_at' => strtotime(Carbon::createFromFormat('Y-m-d H:i:s', $media->created_at)->format('H:i:s d-m-Y')),
                'type' => 'media',
                'assets' => (new AssetsResourceCollection([$media])),
                'applies' => $data,
            ];
        })->toArray();

        return $mediaCollection;
    }

    // get file pdf collection
    public function getFilePdf($collection, $filters)
    {
        return new AssetsResourceCollection($this->getMedia($collection, $filters));
    }

    /**
     * Set the polymorphic relation.
     *
     * @return MorphMany
     */
    public function media(): MorphMany
    {
        return $this->morphMany(config('media-library.media_model'), 'model');
    }

    public function hasPermission($value)
    {
        $permision = [
            static::RESUME_TYPE,
            static::EMPLOYER_TYPE,
            static::PARTNER_TYPE,
            static::OTHER_TYPE,
            static::ADMINISTRATOR_TYPE,
        ];
        if (!empty($value) && in_array($value, $permision) && $this->type == $value) {
            return true;
        }

        return false;
    }

    public function announcedJobs()
    {
        return $this->hasMany(Announcement::class, 'user_id');
    }

    public function searchHistory()
    {
        return $this->hasMany(UserKeyword::class, 'user_id');
    }

    public function isAllowShareCV()
    {
        return $this->allow_share_cv === static::ALLOW_SHARE_CV;
    }

    public function userProfile()
    {
        return $this->hasOne(UserProfile::class, 'user_id');
    }

    public function mainCv()
    {
        return $this->hasOne(UserMainCv::class, 'user_id');
    }

    public function blockedCompanies()
    {
        return $this->belongsToMany(
            Company::class,
            UserBlockCompanies::class,
        )->withTimestamps();
    }

    /**
     * Get all of the Cv Builder for the User
     *
     * @return HasMany
     */
    public function cvBuilders(): HasMany
    {
        return $this->hasMany(MyResume::class);
    }

    public function openToWorkLogs(): HasMany
    {
        return $this->hasMany(UserOTWStatusLog::class);
    }

    /**
     * Set main cv by id, id depends on the type of this id
     *
     * @param  int  $mainCvId  id of the main cv will be set
     * @param  string  $cvType  type of the cv will be relations
     *
     * @return array It contains id and parse stauts. False if not success
     * @throws Exception
     */
    public function setMainCv(int $mainCvId, string $cvType): array
    {
        $mainCv = UserMainCv::updateOrCreate(
            ['user_id' => $this->id],
            [
                'cv_id' => $mainCvId,
                'cv_type' => UserMainCv::getCvTypeRelation($cvType)
            ]
        );

        // Prevent dispatch job if not update/create new
        if (!$mainCv->wasRecentlyCreated && !count($mainCv->changes)) {
            return [
                'id' => $mainCv->id,
                'is_parsed' => $mainCv->wasParsed()
            ];
        }

        // If the relation cv was pased, then dispatch job to update main cv
        $isParsed = false;
        if ($mainCv->wasParsed()) {
            SyncSearchCandidateProcess::dispatch($mainCv->id);
            $isParsed = true;
        } else { // Otherwise, dispatch job to parse cv
            ParseCvUploadProcess::dispatch($mainCv->id);
        }

        return [
            'id' => $mainCv->id,
            'is_parsed' => $isParsed
        ];
    }

    public function cart(): \Illuminate\Database\Eloquent\Relations\HasOne
    {
        return $this->hasOne(Cart::class);
    }

    public function getIsOtwAttribute(): bool
    {
        $otw_status = $this->getTaxonomies('status_works')->pluck('id')->first();

        return $otw_status == Taxonomy::STATUS_WORKS_OPEN;
    }

    public function searchCandidate(): HasOne
    {
        return $this->hasOne(SearchCandidate::class, 'user_id', 'id');
    }

    /**
     * Approve employer account when have first order is completed
     *
     * @param int $orderId
     */
    public function approveEmployerAccount(int $orderId)
    {
        $user = $this;
        if ($user->type == self::EMPLOYER_TYPE && is_null($user->approved_at)) {
            $orderExists = Order::whereIn('user_id', function ($query) use ($user) {
                return $query->select('id')->from('users')->where('company_id', $user->company_id);
            })
            ->where('id', '!=', $orderId)
            ->isCompleted()
            ->exists();

            // If dont have any paid order
            if (!$orderExists) {
                $user->markAccountAsApproved();
            }
        }
    }

    /**
     * Check if user account is frozen
     *
     * @return bool
     */
    public function isFreezing(): bool
    {
        return !empty($this->freeze_at);
    }

    /**
     * Mark user account as frozen
     *
     * @return bool
     */
    public function markAccountAsFreezing(): bool
    {
        return $this->forceFill([
            'freeze_at' => $this->freshTimestamp(),
        ])->save();
    }

    /**
     * Get the user's authentication logs.
     *
     * @return MorphMany
     */
    public function authentications(): MorphMany
    {
        return $this->morphMany(AuthenticationLog::class, 'authenticatable');
    }

    /**
     * Get user's alive tokens.
     *
     * @return MorphMany
     */
    public function aliveTokens(): MorphMany
    {
        return $this->tokens()->where('revoked', false);
    }
}
