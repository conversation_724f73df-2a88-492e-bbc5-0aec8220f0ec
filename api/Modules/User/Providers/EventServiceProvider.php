<?php

namespace Modules\User\Providers;

use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;

class EventServiceProvider extends ServiceProvider
{
    protected $listen = [
        // User events
        \Modules\User\Events\NewUserProcessed::class => [
            \Modules\User\Listeners\PublishEvent::class,
        ],
        \Modules\User\Events\UserHasRecentlyUpdateProfile::class => [
            \Modules\User\Listeners\PublishEvent::class,
        ],
        \Modules\User\Events\PushCreateCv::class => [
            \Modules\User\Listeners\PublishEvent::class,
        ],
        \Modules\User\Events\PushUpdateUserProfile::class => [
            \Modules\User\Listeners\PublishEvent::class,
        ],

        // Authentication events from other modules
        \Modules\Authentication\Events\UserFirstLoginFromWeb::class => [
            \Modules\User\Listeners\UserFirstLoginListener::class,
        ],

        // Passport events
        \Laravel\Passport\Events\RefreshTokenCreated::class => [
            \Modules\User\Listeners\UserHasRecentlyUpdateProfileListener::class,
        ],

        // Signup events
        \Modules\User\Events\SignUp::class => [
            \Modules\User\Listeners\SendEventToGA4GoogleAnalytics::class,
        ],
    ];

    /**
     * Register the listeners for the subscriber.
     *
     * @param  Illuminate\Events\Dispatcher  $events
     */
    protected $subscribe = [
        \Modules\User\Listeners\PublishUserEvent::class,
    ];
}
