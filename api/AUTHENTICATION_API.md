# Authentication API Documentation

## Overview

This document describes the authentication API endpoints that have been migrated from the `accounts` project to the `api` project. The API now supports both traditional email/password authentication and social login (Facebook, X.com/Twitter, Google, LinkedIn, GitHub, Apple).

## Setup

### 1. Install Dependencies

Laravel Socialite has been installed:
```bash
composer require laravel/socialite --ignore-platform-reqs
```

### 2. Environment Configuration

Add the following to your `.env` file:

```env
# Facebook
FACEBOOK_APP_ID=your_facebook_app_id
FACEBOOK_APP_SECRET=your_facebook_app_secret
FACEBOOK_APP_CALLBACK_URL=http://your-domain.com/api/auth/social/facebook/callback

# Twitter/X.com
TWITTER_APP_ID=your_twitter_app_id
TWITTER_APP_SECRET=your_twitter_app_secret
TWITTER_APP_CALLBACK_URL=http://your-domain.com/api/auth/social/twitter/callback

# Google
GOOGLE_APP_ID=your_google_app_id
GOOGLE_APP_SECRET=your_google_app_secret
GOOGLE_APP_CALLBACK_URL=http://your-domain.com/api/auth/social/google/callback

# LinkedIn
LINKEDIN_APP_ID=your_linkedin_app_id
LINKEDIN_APP_SECRET=your_linkedin_app_secret
LINKEDIN_APP_CALLBACK_URL=http://your-domain.com/api/auth/social/linkedin/callback

# GitHub
GITHUB_CLIENT_ID=your_github_client_id
GITHUB_CLIENT_SECRET=your_github_client_secret
GITHUB_APP_CALLBACK_URL=http://your-domain.com/api/auth/social/github/callback

# Apple
SIGN_IN_WITH_APPLE_CLIENT_ID=your_apple_client_id
SIGN_IN_WITH_APPLE_CLIENT_SECRET=your_apple_client_secret
SIGN_IN_WITH_APPLE_REDIRECT=http://your-domain.com/api/auth/social/apple/callback
```

### 3. Database Migration

Run the migration to create the socials table:
```bash
php artisan migrate
```

### 4. Passport Setup

If not already done, install Passport keys:
```bash
php artisan passport:install
```

## API Endpoints

### Authentication Endpoints

#### 1. Register
- **URL**: `POST /api/auth/register`
- **Body**:
```json
{
    "email": "<EMAIL>",
    "password": "password123",
    "password_confirmation": "password123",
    "display_name": "John Doe",
    "type": "resume"
}
```
- **Response**:
```json
{
    "success": true,
    "message": "Registration successful",
    "data": {
        "user": {
            "id": "uuid",
            "email": "<EMAIL>",
            "display_name": "John Doe",
            "type": "resume"
        },
        "access_token": "token_here",
        "token_type": "Bearer"
    }
}
```

#### 2. Login
- **URL**: `POST /api/auth/login`
- **Body**:
```json
{
    "email": "<EMAIL>",
    "password": "password123"
}
```
- **Response**:
```json
{
    "success": true,
    "message": "Login successful",
    "data": {
        "user": {
            "id": "uuid",
            "email": "<EMAIL>",
            "display_name": "John Doe"
        },
        "access_token": "token_here",
        "token_type": "Bearer"
    }
}
```

#### 3. Get Current User
- **URL**: `GET /api/auth/me`
- **Headers**: `Authorization: Bearer {token}`
- **Response**:
```json
{
    "success": true,
    "data": {
        "user": {
            "id": "uuid",
            "email": "<EMAIL>",
            "display_name": "John Doe"
        }
    }
}
```

#### 4. Logout
- **URL**: `POST /api/auth/logout`
- **Headers**: `Authorization: Bearer {token}`
- **Response**:
```json
{
    "success": true,
    "message": "Logout successful",
    "data": []
}
```

### Social Authentication Endpoints

#### 1. Get Social Login URL
- **URL**: `GET /api/auth/social/{provider}`
- **Providers**: `facebook`, `twitter`, `google`, `linkedin`, `github`, `apple`
- **Response**:
```json
{
    "success": true,
    "message": "Redirect URL generated successfully",
    "data": {
        "redirect_url": "https://provider.com/oauth/authorize?..."
    }
}
```

#### 2. Handle Social Callback
- **URL**: `POST /api/auth/social/{provider}/callback`
- **Body**: Include the authorization code or token from the social provider
- **Response**:
```json
{
    "success": true,
    "message": "Social login successful",
    "data": {
        "user": {
            "id": "uuid",
            "email": "<EMAIL>",
            "display_name": "John Doe"
        },
        "access_token": "token_here",
        "token_type": "Bearer",
        "provider": "facebook"
    }
}
```

#### 3. Get Linked Social Accounts
- **URL**: `GET /api/auth/social/accounts`
- **Headers**: `Authorization: Bearer {token}`
- **Response**:
```json
{
    "success": true,
    "data": {
        "linked_accounts": [
            {
                "id": 1,
                "provider": "facebook",
                "name": "John Doe",
                "email": "<EMAIL>"
            }
        ]
    }
}
```

#### 4. Link Social Account
- **URL**: `POST /api/auth/social/{provider}/link`
- **Headers**: `Authorization: Bearer {token}`
- **Response**:
```json
{
    "success": true,
    "message": "Social account linked successfully",
    "data": []
}
```

#### 5. Unlink Social Account
- **URL**: `DELETE /api/auth/social/{provider}/unlink`
- **Headers**: `Authorization: Bearer {token}`
- **Response**:
```json
{
    "success": true,
    "message": "Social account unlinked successfully",
    "data": []
}
```

## Testing

Run the authentication tests:
```bash
php artisan test Modules/Authentication/Tests/Feature/AuthenticationTest.php
```

## Migration Notes

### What was moved from `accounts` to `api`:

1. **Authentication Logic**: Login, register, logout functionality
2. **Social Login**: Support for Facebook, Twitter, Google, LinkedIn, GitHub, Apple
3. **User Management**: User profile, account freezing, approval system
4. **Token Management**: Passport token handling with proper expiration
5. **Validation**: Request validation for all authentication endpoints

### Key Improvements:

1. **API-First Design**: All endpoints return JSON responses
2. **Better Error Handling**: Consistent error responses with proper HTTP status codes
3. **Request Validation**: Dedicated request classes for validation
4. **Testing**: Comprehensive test coverage for all endpoints
5. **Documentation**: Complete API documentation

### Database Changes:

1. **Socials Table**: New table to store social login information
2. **User Model**: Added `isFreezing()` and `markAccountAsFreezing()` methods
3. **Fillable Fields**: Added `email_verified_at` and `freeze_at` to User model

## Security Features

1. **Token Expiration**: Configurable token expiration times
2. **Account Freezing**: Support for freezing user accounts
3. **Email Verification**: Built-in email verification support
4. **Social Account Linking**: Secure linking/unlinking of social accounts
5. **CORS Support**: Proper CORS handling for social authentication
