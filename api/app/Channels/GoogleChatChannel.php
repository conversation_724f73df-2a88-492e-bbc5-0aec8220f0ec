<?php

namespace App\Channels;

use GuzzleHttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Notifications\Notification;

class GoogleChatChannel
{
    /**
     * @param $notifiable
     * @param  Notification  $notification
     *
     * @return void
     * @throws GuzzleException
     */
    public function send($notifiable, Notification $notification)
    {
        /** @phpstan-ignore-next-line */
        $message = $notification->toGoogleChat($notifiable);
        $url = config('services.google-channel.sale');
        $client = new Client();
        $client->post($url, [
            'headers' => [
                'Content-Type' => 'application/json',
            ],
            'json' => [
                'text' => $message,
            ],
        ]);
    }
}
