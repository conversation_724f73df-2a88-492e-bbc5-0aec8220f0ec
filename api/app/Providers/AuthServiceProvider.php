<?php

namespace App\Providers;

use App\Entities\AuthCode;
use App\Entities\Client;
use App\Entities\PersonalAccessClient;
use App\Entities\Token;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use Laravel\Passport\Passport;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The policy mappings for the application.
     *
     * @var array
     */
    protected $policies = [
        // 'App\Model' => 'App\Policies\ModelPolicy',
    ];

    /**
     * Register any authentication / authorization services.
     *
     * @return void
     */
    public function boot()
    {
        $this->registerPolicies();

        Passport::useClientModel(\Laravel\Passport\Client::class);
        Passport::useTokenModel(Token::class);
        Passport::useAuthCodeModel(AuthCode::class);
        Passport::usePersonalAccessClientModel(PersonalAccessClient::class);

        // Configure Passport
        Passport::tokensExpireIn(now()->addHours(24)); // 24 hours
        Passport::refreshTokensExpireIn(now()->addHours(8760)); // 1 nam
        Passport::personalAccessTokensExpireIn(now()->addMonths(6)); // 6 months
    }
}
