<?php

namespace App\Services;

use App\Contracts\GA4EventInterface;
use Illuminate\Support\Facades\Log;
use Modules\User\Listeners\SendEventToGA4GoogleAnalytics;

class GA4EventDispatcher
{
    /**
     * Dispatch GA4 event to analytics.
     *
     * @param GA4EventInterface $event
     * @return void
     */
    public static function dispatch(GA4EventInterface $event): void
    {
        try {
            $listener = new SendEventToGA4GoogleAnalytics();
            $listener->handle($event);
        } catch (\Exception $e) {
            Log::error('[GA4EventDispatcher] Failed to dispatch GA4 event', [
                'event_class' => get_class($event),
                'error' => $e->getMessage(),
            ]);
        }
    }
}
