{"name": "laravel/laravel", "type": "project", "description": "Applancer MS base Laravel.", "authors": [{"name": "sonnx", "email": "<EMAIL>"}], "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "~8.2.0", "ext-json": "*", "ext-pdo": "*", "anhskohbo/no-captcha": "^3.3", "arcanedev/laravel-notes": "^10.0", "astrotomic/laravel-translatable": "^11.9", "barryvdh/laravel-debugbar": "^3.2", "barryvdh/laravel-dompdf": "^2.0", "bordoni/phpass": "dev-main", "bschmitt/laravel-amqp": "^2.0", "chelout/laravel-relationship-events": "^2.0", "cyrildewit/eloquent-viewable": "^7.0", "doctrine/dbal": "^3.0", "fakerphp/faker": "^1.23", "genealabs/laravel-model-caching": "^11.0", "google/apiclient": "^2.4", "google/recaptcha": "^1.3", "guzzlehttp/guzzle": "^7.0.1", "guzzlehttp/psr7": "^2.6", "irazasyed/telegram-bot-sdk": "^3.0", "jackpopp/geodistance": "dev-master", "jenssegers/agent": "^2.6", "kalnoy/nestedset": "^6.0", "kreait/laravel-firebase": "^5.0", "laravel/framework": "^10.0", "laravel/legacy-factories": "^1.4", "laravel/passport": "^11.0", "laravel/sanctum": "^3.2", "laravel/scout": "^10.8", "laravel/socialite": "*", "laravel/tinker": "^2.9", "league/csv": "^9.8", "league/flysystem": "^3.0", "matchish/laravel-scout-elasticsearch": "^7.0", "matthiasmullie/minify": "^1.3", "nwidart/laravel-modules": "^6.0", "overtrue/laravel-follow": "^1.1", "owen-it/laravel-auditing": "^13.0", "php-http/message": "^1.16", "plank/laravel-metable": "^5.0", "predis/predis": "^2.2", "psr/http-factory": "*", "redmix0901/core": "dev-master", "redmix0901/elastic-resource": "^1.0", "redmix0901/oauth2-client-sso": "^1.2", "revolution/laravel-google-sheets": "^6.0", "sentry/sentry-laravel": "^4.2", "spatie/laravel-medialibrary": "^10.0", "spatie/laravel-model-states": "^2.7", "spatie/laravel-query-builder": "^5.0", "symfony/dom-crawler": "~3.1|~4.0", "thunderer/shortcode": "^0.6.2", "unleash/client": "^2.3"}, "require-dev": {"barryvdh/laravel-ide-helper": "^2.8", "driftingly/rector-laravel": "^1.0", "filp/whoops": "^2.0", "larastan/larastan": "*", "laravel/pint": "^1.17", "mockery/mockery": "^1.0", "nunomaduro/collision": "^7.0", "pestphp/pest": "^2.34", "rector/rector": "^1.0", "spatie/laravel-ignition": "^2.0", "symfony/css-selector": "~3.1"}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"php-http/discovery": true, "pestphp/pest-plugin": true}}, "extra": {"laravel": {"dont-discover": ["laravel/horizon"], "providers": []}}, "autoload": {"psr-4": {"App\\": "app/", "Modules\\": "Mo<PERSON>les/"}, "files": ["app/helpers.php", "app/Helpers/ga4_helpers.php"], "classmap": ["database/seeds", "database/factories"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "minimum-stability": "dev", "prefer-stable": true, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"], "setup": ["@composer clear", "@php artisan module:migrate", "@php artisan migrate", "@php artisan db:seed"], "admin-install": ["rm -rf app/Admin", "@php artisan admin:install"], "import-extension": ["@php artisan admin:import log-viewer", "@php artisan admin:import config", "@php artisan admin:import helpers", "@php artisan admin:import lock-screen", "@php artisan admin:import scheduling", "@php artisan admin:import redis-manager", "@php artisan admin:import media-manager", "@php artisan admin:import tinymce", "@php artisan admin:import jsonEditor", "@php artisan admin:import composer-viewer"], "modules": ["@php artisan module:seed --force"], "clear": ["@php artisan cache:clear", "@php artisan config:clear", "@php artisan view:clear", "@php artisan clear-compiled", "@php artisan event:clear", "@php artisan optimize:clear", "@composer dump-autoload"], "all-scripts": ["@composer setup", "@composer admin-install", "@composer import-extension", "@composer modules"], "sniff": ["./tools/vendor/bin/php-cs-fixer fix -vvv --dry-run --show-progress=dots"], "fix": ["./tools/vendor/bin/php-cs-fixer fix -vvv --show-progress=dots"], "analyse": ["./vendor/bin/phpstan --memory-limit=512M analyse"], "test": ["./vendor/bin/phpunit --coverage-text --colors=never"]}}