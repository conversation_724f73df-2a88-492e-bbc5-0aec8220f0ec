<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Authentication Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains configuration options for the authentication module.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Client IDs
    |--------------------------------------------------------------------------
    |
    | These are the client IDs for different platforms.
    |
    */
    'client_mobile' => env('PASSPORT_MOBILE_CLIENT_ID', 2),
    'client_web' => env('PASSPORT_WEB_CLIENT_ID', 1),

    /*
    |--------------------------------------------------------------------------
    | FCM Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for Firebase Cloud Messaging.
    |
    */
    'fcm' => [
        'server_key' => env('FCM_SERVER_KEY'),
        'sender_id' => env('FCM_SENDER_ID'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Queue Configuration
    |--------------------------------------------------------------------------
    |
    | Queue names for different authentication jobs.
    |
    */
    'queues' => [
        'analytics' => env('QUEUE_ANALYTICS', 'analytics'),
        'notifications' => env('QUEUE_NOTIFICATIONS', 'notifications'),
        'fcm_tokens' => env('QUEUE_FCM_TOKENS', 'topdev.create_fcm_device_token_activity'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Analytics Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for analytics tracking.
    |
    */
    'analytics' => [
        'enabled' => env('ANALYTICS_ENABLED', true),
        'ga4_enabled' => env('GA4_ENABLED', true),
        'custom_analytics_enabled' => env('CUSTOM_ANALYTICS_ENABLED', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Login Attempt Limits
    |--------------------------------------------------------------------------
    |
    | Configuration for login attempt limits and lockouts.
    |
    */
    'login_attempts' => [
        'max_attempts' => env('LOGIN_MAX_ATTEMPTS', 5),
        'lockout_duration' => env('LOGIN_LOCKOUT_DURATION', 900), // 15 minutes
    ],

    /*
    |--------------------------------------------------------------------------
    | Token Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for various tokens.
    |
    */
    'tokens' => [
        'verification_code_expiry' => env('VERIFICATION_CODE_EXPIRY', 300), // 5 minutes
        'password_reset_expiry' => env('PASSWORD_RESET_EXPIRY', 3600), // 1 hour
    ],

    /*
    |--------------------------------------------------------------------------
    | Social Login Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for social login providers.
    |
    */
    'social' => [
        'enabled_providers' => [
            'facebook',
            'twitter',
            'google',
            'linkedin',
            'github',
            'apple',
        ],
        'auto_create_user' => env('SOCIAL_AUTO_CREATE_USER', true),
        'auto_verify_email' => env('SOCIAL_AUTO_VERIFY_EMAIL', true),
    ],

    /*
    |--------------------------------------------------------------------------
    | Notification Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for authentication notifications.
    |
    */
    'notifications' => [
        'welcome_delay_hours' => env('WELCOME_NOTIFICATION_DELAY_HOURS', 1),
        'push_create_cv_delay_hours' => env('PUSH_CREATE_CV_DELAY_HOURS', 1),
        'push_update_profile_delay_hours' => env('PUSH_UPDATE_PROFILE_DELAY_HOURS', 24),
    ],

    /*
    |--------------------------------------------------------------------------
    | Analytics Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for analytics tracking and broadcasting.
    |
    */
    'analytics' => [
        'ga4_enabled' => env('AUTH_GA4_ENABLED', true),
        'broadcast_to_analytics' => env('AUTH_BROADCAST_ANALYTICS', true),
        'track_login_events' => env('AUTH_TRACK_LOGIN_EVENTS', true),
        'track_signup_events' => env('AUTH_TRACK_SIGNUP_EVENTS', true),
    ],
];
