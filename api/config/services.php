<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'mailgun' => [
        'domain' => env('MAILGUN_DOMAIN'),
        'secret' => env('MAILGUN_SECRET'),
        'endpoint' => env('MAILGUN_ENDPOINT', 'api.mailgun.net'),
    ],

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],

    'facebook' => [
        'client_id' => env('FACEBOOK_APP_ID'),
        'client_secret' => env('FACEBOOK_APP_SECRET'),
        'redirect' => env('FACEBOOK_APP_CALLBACK_URL'),
    ],

    'twitter' => [
        'client_id' => env('TWITTER_APP_ID'),
        'client_secret' => env('TWITTER_APP_SECRET'),
        'redirect' => env('TWITTER_APP_CALLBACK_URL'),
    ],

    'google' => [
        'client_id' => env('GOOGLE_APP_ID'),
        'client_secret' => env('GOOGLE_APP_SECRET'),
        'redirect' => env('GOOGLE_APP_CALLBACK_URL'),
    ],

    'linkedin' => [
        'client_id' => env('LINKEDIN_APP_ID'),
        'client_secret' => env('LINKEDIN_APP_SECRET'),
        'redirect' => env('LINKEDIN_APP_CALLBACK_URL'),
    ],

    'github' => [
        'client_id' => env('GITHUB_CLIENT_ID'),
        'client_secret' => env('GITHUB_CLIENT_SECRET'),
        'redirect' => env('GITHUB_APP_CALLBACK_URL'),
    ],

    'apple' => [
        'client_id' => env('SIGN_IN_WITH_APPLE_CLIENT_ID'),
        'client_secret' => env('SIGN_IN_WITH_APPLE_CLIENT_SECRET'),
        'redirect' => env('SIGN_IN_WITH_APPLE_REDIRECT'),
    ],

    'ga4' => [
        [
            'api_secret' => env('GA4_API_SECRET_1', 'oqerIAwmRhCJHGByRtloTA'),
            'measurement_id' => env('GA4_MEASUREMENT_ID_1', 'G-CKC0THVHF7'),
        ],
        [
            'api_secret' => env('GA4_API_SECRET_2', '2Kh2hOxSREaVYgr32QXgpA'),
            'measurement_id' => env('GA4_MEASUREMENT_ID_2', 'G-KSXMBVZ4MS'),
        ],
    ],

    'telegram-bot-api' => [
        'chat_id' => env('TELEGRAM_BOT_ID'),
        'token' => env('TELEGRAM_BOT_TOKEN', '*********************************************')
    ],

    'unleash' => [
        'app_url' => env('UNLEASH_APP_URL', 'https://git.topdev.asia/api/v4/feature_flags/unleash/78'),
        'instance_id' => env('UNLEASH_INSTANCE_ID', 'hVXa4LEThVuxt84SdEkx'),
    ],

    'google-channel' => [
        'sale' => env('GOOGLE_CHAT_CS_SPACE')
    ],

    'crm' => [
        'base_url' => env('CRM_API_BASE_URL', 'https://crmdev.topdev.asia'),
        'timeout' => 30,
        'auth_code' => env('CRM_API_AUTH_CODE', 'LS0tLS1CRUdJTiBQVUJMSUMgS0VZLS0tLS0KTUZ3d0RRWUpLb1pJaHZjTkFRRUJCUUFEU3dBd1NBSkJBTkdWNGdzUHk3bzlEcVI3TzQ3K0ZpM0ltNEROa2h3dAp2Nk9OSkJoLytjYkJSZlRnSGRGcExSS1NQTDZ4eHozSjFRK0Y2WlBRR0YrZ2dlald0YStXVHZjQ0F3RUFBUT09Ci0tLS0tRU5EIFBVQkxJQyBLRVktLS0tLQo='),
    ],
    'google_recaptcha' => [
        'site_key' => env('RECAPTCHA_SITE_KEY'),
        'secret_key' => env('RECAPTCHA_SECRET_KEY'),
        'enable' => env('RECAPTCHA_ENABLE'),
    ]
];
