# Authentication Migration - From Accounts to API

## Tổng quan
Dự án này đã di chuyển toàn bộ logic authentication từ project `accounts` sang project `api` và thêm hỗ trợ Laravel Socialite cho đăng nhập qua Facebook và X.com (Twitter).

## Những gì đã hoàn thành

### 1. Cài đặt Laravel Socialite
- ✅ Đã cài đặt Laravel Socialite vào project API
- ✅ Cấu hình services.php cho Facebook, Twitter, Google, LinkedIn, GitHub, Apple

### 2. Tạo Authentication Controllers
- ✅ **AuthenticationController**: Xử lý login, register, logout, profile
- ✅ **SocialAuthController**: Xử lý social login cho tất cả providers
- ✅ **VerificationCodeController**: Xử lý verification code (đã có sẵn)

### 3. Tạo Models và Migrations
- ✅ **Social Model**: Quản lý liên kết social accounts
- ✅ **Migration**: Tạo bảng socials với cấu trúc đầy đủ
- ✅ **User Model**: Thêm methods `isFreezing()`, `markAccountAsFreezing()`

### 4. Request Validation
- ✅ **LoginRequest**: Validation cho đăng nhập
- ✅ **RegisterRequest**: Validation cho đăng ký

### 5. API Routes
- ✅ **Public Routes**:
  - `POST /api/auth/login` - Đăng nhập
  - `POST /api/auth/register` - Đăng ký
  - `GET /api/auth/social/{provider}` - Redirect to social provider
  - `POST /api/auth/social/{provider}/callback` - Handle social callback
  - `POST /api/auth/verification/send` - Gửi verification code
  - `POST /api/auth/verification/verify` - Verify code

- ✅ **Protected Routes** (require authentication):
  - `GET /api/auth/me` - Lấy thông tin user
  - `POST /api/auth/logout` - Đăng xuất
  - `GET /api/auth/social/accounts` - Lấy danh sách social accounts
  - `POST /api/auth/social/{provider}/link` - Liên kết social account
  - `DELETE /api/auth/social/{provider}/unlink` - Hủy liên kết social account
  - `GET /api/auth/logs` - Lấy authentication logs

### 6. Passport Configuration
- ✅ Cấu hình Passport trong AuthServiceProvider
- ✅ Token expiration: 24 hours
- ✅ Refresh token: 30 days
- ✅ Personal access token: 6 months

### 7. Testing
- ✅ Tạo Feature Tests cho authentication endpoints

## Cấu hình cần thiết

### Environment Variables
Thêm các biến sau vào file `.env`:

```env
# Facebook
FACEBOOK_APP_ID=your_facebook_app_id
FACEBOOK_APP_SECRET=your_facebook_app_secret
FACEBOOK_APP_CALLBACK_URL=http://your-domain.com/api/auth/social/facebook/callback

# Twitter/X.com
TWITTER_APP_ID=your_twitter_app_id
TWITTER_APP_SECRET=your_twitter_app_secret
TWITTER_APP_CALLBACK_URL=http://your-domain.com/api/auth/social/twitter/callback

# Google (optional)
GOOGLE_APP_ID=your_google_app_id
GOOGLE_APP_SECRET=your_google_app_secret
GOOGLE_APP_CALLBACK_URL=http://your-domain.com/api/auth/social/google/callback

# LinkedIn (optional)
LINKEDIN_APP_ID=your_linkedin_app_id
LINKEDIN_APP_SECRET=your_linkedin_app_secret
LINKEDIN_APP_CALLBACK_URL=http://your-domain.com/api/auth/social/linkedin/callback

# GitHub (optional)
GITHUB_CLIENT_ID=your_github_client_id
GITHUB_CLIENT_SECRET=your_github_client_secret
GITHUB_APP_CALLBACK_URL=http://your-domain.com/api/auth/social/github/callback

# Apple (optional)
SIGN_IN_WITH_APPLE_CLIENT_ID=your_apple_client_id
SIGN_IN_WITH_APPLE_CLIENT_SECRET=your_apple_client_secret
SIGN_IN_WITH_APPLE_REDIRECT=http://your-domain.com/api/auth/social/apple/callback
```

### Database Setup
1. Chạy migration để tạo bảng socials:
```bash
php artisan migrate
```

2. Cài đặt Passport (nếu chưa có):
```bash
php artisan passport:install
```

## Cách sử dụng

### 1. Đăng ký user mới
```bash
POST /api/auth/register
Content-Type: application/json

{
    "email": "<EMAIL>",
    "password": "password123",
    "password_confirmation": "password123",
    "display_name": "John Doe",
    "type": "resume"
}
```

### 2. Đăng nhập
```bash
POST /api/auth/login
Content-Type: application/json

{
    "email": "<EMAIL>",
    "password": "password123"
}
```

### 3. Social Login
```bash
# Bước 1: Lấy redirect URL
GET /api/auth/social/facebook

# Bước 2: Sau khi user authorize, gọi callback
POST /api/auth/social/facebook/callback
Content-Type: application/json

{
    "code": "authorization_code_from_facebook"
}
```

### 4. Lấy thông tin user
```bash
GET /api/auth/me
Authorization: Bearer {access_token}
```

### 5. Đăng xuất
```bash
POST /api/auth/logout
Authorization: Bearer {access_token}
```

## Supported Social Providers
- ✅ Facebook
- ✅ Twitter/X.com
- ✅ Google
- ✅ LinkedIn
- ✅ GitHub
- ✅ Apple

## Các bước tiếp theo cần thực hiện

1. **Setup Database**: Cấu hình kết nối database và chạy migrations
2. **Setup Passport**: Chạy `php artisan passport:install`
3. **Test Endpoints**: Chạy tests để đảm bảo mọi thứ hoạt động
4. **Frontend Integration**: Cập nhật frontend để sử dụng API endpoints mới
5. **Cleanup**: Xóa code cũ trong project accounts (nếu cần)

## Testing
Chạy tests để kiểm tra:
```bash
php artisan test Modules/Authentication/Tests/Feature/AuthenticationTest.php
```

## Notes
- Tất cả endpoints trả về JSON response với format chuẩn
- Social login hỗ trợ tự động tạo user mới nếu chưa tồn tại
- Hỗ trợ link/unlink multiple social accounts cho một user
- Authentication logs được ghi lại tự động
- Token có thể được revoke khi logout
