#!/bin/bash

echo "🚀 Setting up Authentication Migration..."

# Check if .env exists
if [ ! -f .env ]; then
    echo "❌ .env file not found. Please copy .env.example to .env first."
    exit 1
fi

echo "📦 Installing dependencies..."
composer install --ignore-platform-reqs

echo "🔑 Generating application key..."
php artisan key:generate

echo "🗄️ Running migrations..."
php artisan migrate

echo "🔧 Fixing socials table schema..."
php artisan migrate --path=database/migrations/2025_08_04_020000_fix_socials_provider_id_column.php

echo "🔐 Installing Passport..."
php artisan passport:install

echo "📋 Publishing config files..."
php artisan vendor:publish --provider="Laravel\Passport\PassportServiceProvider"

echo "⚙️ Setting up queues..."
php artisan queue:table
php artisan migrate

echo "🧹 Clearing cache..."
php artisan config:clear
php artisan cache:clear
php artisan route:clear
php artisan event:clear

echo "🔄 Optimizing..."
php artisan config:cache
php artisan route:cache
php artisan event:cache

echo "✅ Setup completed!"
echo ""
echo "📋 Next steps:"
echo "1. Configure your social provider credentials in .env"
echo "2. Update your frontend to use the new API endpoints"
echo "3. Run tests: php artisan test Modules/Authentication/Tests/Feature/AuthenticationTest.php"
echo ""
echo "📚 Available endpoints:"
echo "- POST /api/auth/register"
echo "- POST /api/auth/login"
echo "- GET /api/auth/social/{provider}"
echo "- POST /api/auth/social/{provider}/callback"
echo "- GET /api/auth/me (authenticated)"
echo "- POST /api/auth/logout (authenticated)"
echo "- GET /api/auth/social/accounts (authenticated)"
echo "- POST /api/auth/social/{provider}/link (authenticated)"
echo "- DELETE /api/auth/social/{provider}/unlink (authenticated)"
echo ""
echo "🔄 Queue workers (run in separate terminals):"
echo "- php artisan queue:work --queue=analytics"
echo "- php artisan queue:work --queue=notifications"
echo "- php artisan queue:work --queue=topdev.create_fcm_device_token_activity"
echo ""
echo "🎉 Complete Authentication migration is ready!"
echo "📖 Check AUTHENTICATION_MIGRATION.md for detailed documentation"
