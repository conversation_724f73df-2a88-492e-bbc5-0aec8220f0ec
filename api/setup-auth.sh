#!/bin/bash

echo "🚀 Setting up Authentication Migration..."

# Check if .env exists
if [ ! -f .env ]; then
    echo "❌ .env file not found. Please copy .env.example to .env first."
    exit 1
fi

echo "📦 Installing dependencies..."
composer install --ignore-platform-reqs

echo "🔑 Generating application key..."
php artisan key:generate

echo "🗄️ Running migrations..."
php artisan migrate

echo "🔐 Installing Passport..."
php artisan passport:install

echo "🧹 Clearing cache..."
php artisan config:clear
php artisan cache:clear
php artisan route:clear

echo "🔄 Optimizing..."
php artisan config:cache
php artisan route:cache

echo "✅ Setup completed!"
echo ""
echo "📋 Next steps:"
echo "1. Configure your social provider credentials in .env"
echo "2. Update your frontend to use the new API endpoints"
echo "3. Run tests: php artisan test Modules/Authentication/Tests/Feature/AuthenticationTest.php"
echo ""
echo "📚 Available endpoints:"
echo "- POST /api/auth/register"
echo "- POST /api/auth/login"
echo "- GET /api/auth/social/{provider}"
echo "- POST /api/auth/social/{provider}/callback"
echo "- GET /api/auth/me (authenticated)"
echo "- POST /api/auth/logout (authenticated)"
echo ""
echo "🎉 Authentication migration is ready!"
