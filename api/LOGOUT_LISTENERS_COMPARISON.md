# LogSuccessfulLogout Listeners Comparison

## 🔍 **Sự khác biệt giữa Accounts và API**

### **Accounts Project có 2 LogSuccessfulLogout listeners:**

#### **1. `app/Listeners/LogSuccessfulLogout.php` - Token Revocation**
```php
class LogSuccessfulLogout
{
    public function handle(Logout $logout)
    {
        Log::info('Starting revoke token user: [' . $logout->user->email . '] #' . $logout->user->getKey());

        $tokens = $logout->user->aliveTokens;
        $tokens = $this->tokensShouldBeRevoking($logout, $tokens);

        foreach ($tokens as $token) {
            $token->revoke();
            Log::info('Token ID has revoked: ' . $token->getKey());
            if ($token->refreshToken) {
                $token->refreshToken->revoke();
            }
        }
    }

    protected function tokensShouldBeRevoking(Logout $logout, $tokens)
    {
        if ($this->isGuardWeb($logout) && !$this->userIsFreezing($logout)) {
            $tokens = $tokens->whereNotIn(
                'client_id',
                [config('authenlog.client_mobile')]
            );
        }
        return $tokens;
    }

    protected function isGuardWeb(Logout $logout)
    {
        return $logout->guard === 'web';
    }

    protected function userIsFreezing(Logout $logout)
    {
        return $logout->user->isFreezing($logout);
    }
}
```

**Logic:**
- ✅ **Web logout**: Không revoke mobile tokens (trừ khi user freezing)
- ✅ **API logout**: Revoke tất cả tokens
- ✅ **User freezing**: Revoke tất cả tokens
- ✅ **Revoke refresh tokens**: Cùng với access tokens

#### **2. `Modules/AuthenLog/Listeners/LogSuccessfulLogout.php` - Authentication Log**
```php
class LogSuccessfulLogout
{
    public function handle(Logout $event)
    {
        if ($event->user) {
            $user = $event->user;
            $ip = $this->request->ip();
            $userAgent = $this->request->userAgent();
            $authenticationLog = $user->authentications()
                ->whereIpAddress($ip)
                ->whereUserAgent($userAgent)
                ->first();

            if (!$authenticationLog) {
                $authenticationLog = new AuthenLog([
                    'ip_address' => $ip,
                    'user_agent' => $userAgent,
                ]);
            }

            $authenticationLog->logout_at = Carbon::now();
            $user->authentications()->save($authenticationLog);
        }
    }
}
```

**Logic:**
- ✅ **Find existing log**: Tìm authentication log theo IP + User-Agent
- ✅ **Create if not exists**: Tạo mới nếu không tìm thấy
- ✅ **Update logout_at**: Set thời gian logout

### **API Project (đã cập nhật):**

#### **1. `LogSuccessfulLogout.php` - Token Revocation (MATCH ACCOUNTS)**
```php
class LogSuccessfulLogout
{
    public function handle(Logout $logout)
    {
        Log::info('Starting revoke token user: [' . $logout->user->email . '] #' . $logout->user->id);

        $tokens = $logout->user->aliveTokens;
        $tokens = $this->tokensShouldBeRevoking($logout, $tokens);

        foreach ($tokens as $token) {
            $token->revoke();
            Log::info('Token ID has revoked: ' . $token->id);
            if ($token->refreshToken) {
                $token->refreshToken->revoke();
            }
        }
    }

    protected function tokensShouldBeRevoking(Logout $logout, $tokens)
    {
        if ($this->isGuardWeb($logout) && !$this->userIsFreezing($logout)) {
            $tokens = $tokens->whereNotIn(
                'client_id',
                [config('authentication.client_mobile')]
            );
        }
        return $tokens;
    }

    protected function isGuardWeb(Logout $logout)
    {
        return $logout->guard === 'web';
    }

    protected function userIsFreezing(Logout $logout)
    {
        return $logout->user->isFreezing();
    }
}
```

#### **2. `LogSuccessfulLogoutAuthLog.php` - Authentication Log (MATCH ACCOUNTS)**
```php
class LogSuccessfulLogoutAuthLog
{
    public function handle(Logout $event)
    {
        if ($event->user) {
            $user = $event->user;
            $ip = $this->request->ip();
            $userAgent = $this->request->userAgent();
            $authenticationLog = $user->authentications()
                ->whereIpAddress($ip)
                ->whereUserAgent($userAgent)
                ->first();

            if (!$authenticationLog) {
                $authenticationLog = new AuthenticationLog([
                    'ip_address' => $ip,
                    'user_agent' => $userAgent,
                ]);
            }

            $authenticationLog->logout_at = Carbon::now();
            $user->authentications()->save($authenticationLog);
        }
    }
}
```

## 🔧 **EventServiceProvider Mapping**

### **Accounts:**
```php
// app/Providers/EventServiceProvider.php
Logout::class => [
    LogSuccessfulLogoutWebDevice::class,
    LogSuccessfulLogout::class  // Token revocation
],

// Modules/AuthenLog/Providers/EventServiceProvider.php
'Illuminate\Auth\Events\Logout' => [
    '\Modules\AuthenLog\Listeners\LogSuccessfulLogout',  // Auth log
],
```

### **API (Updated):**
```php
\Illuminate\Auth\Events\Logout::class => [
    \Modules\Authentication\Listeners\LogSuccessfulLogout::class,        // Token revocation
    \Modules\Authentication\Listeners\LogSuccessfulLogoutWebDevice::class, // FCM handling
    \Modules\Authentication\Listeners\LogSuccessfulLogoutAuthLog::class,   // Auth log
],
```

## 🎯 **Key Differences Fixed:**

### **1. Token Revocation Logic:**
- ✅ **Web logout**: Giữ mobile tokens (trừ khi freezing)
- ✅ **API logout**: Revoke tất cả tokens
- ✅ **Freezing check**: Revoke tất cả nếu user freezing
- ✅ **Refresh tokens**: Revoke cùng với access tokens

### **2. Authentication Logging:**
- ✅ **Find existing log**: Theo IP + User-Agent
- ✅ **Create if missing**: Tạo mới authentication log
- ✅ **Update logout_at**: Set thời gian logout

### **3. Configuration:**
- ✅ **Mobile client ID**: `config('authentication.client_mobile')`
- ✅ **User relationships**: `aliveTokens`, `authentications()`
- ✅ **User methods**: `isFreezing()`

## 🚀 **Flow hoàn chỉnh:**

```
User Logout Event
├── LogSuccessfulLogout (Token Revocation)
│   ├── Check guard (web vs api)
│   ├── Check user freezing status
│   ├── Filter tokens to revoke
│   └── Revoke access + refresh tokens
├── LogSuccessfulLogoutWebDevice (FCM Handling)
│   ├── Check web guard
│   ├── Get FCM token from cookie
│   └── Dispatch CreateFcmDeviceTokenActivity
└── LogSuccessfulLogoutAuthLog (Authentication Log)
    ├── Find existing auth log (IP + User-Agent)
    ├── Create new if not found
    └── Update logout_at timestamp
```

## ✅ **Verification:**

- [x] **Token revocation logic** matches accounts exactly
- [x] **Authentication logging** matches accounts exactly
- [x] **FCM token handling** matches accounts exactly
- [x] **Event listener mapping** complete
- [x] **User model relationships** (`aliveTokens`, `authentications`)
- [x] **Configuration mapping** (`client_mobile`)
- [x] **Database schema** (`logout_at` field)

**✅ API project giờ đã có CHÍNH XÁC cùng logic logout như accounts project!**
