<?php

namespace Modules\VerificationCode\Providers;

use Illuminate\Database\Eloquent\Factory;
use Illuminate\Support\ServiceProvider;

class VerificationCodeServiceProvider extends ServiceProvider
{
    /**
     * Boot the application events.
     *
     * @return void
     */
    public function boot()
    {
        $this->registerTranslations();
        $this->registerConfig();
        $this->registerViews();
        $this->registerFactories();
        $this->loadMigrationsFrom(module_path('VerificationCode', 'Database/Migrations'));
    }

    /**
     * Register the service provider.
     *
     * @return void
     */
    public function register()
    {
        $this->app->register(RouteServiceProvider::class);

        $this->app->bind('verification-code', function () {
            return new \Modules\VerificationCode\Support\VerificationCodeManager();
        });
    }

    /**
     * Register config.
     *
     * @return void
     */
    protected function registerConfig()
    {
        $this->publishes([
            module_path('VerificationCode', 'Config/config.php') => config_path('verificationcode.php'),
        ], 'config');
        $this->mergeConfigFrom(
            module_path('VerificationCode', 'Config/config.php'),
            'verificationcode'
        );
    }

    /**
     * Register views.
     *
     * @return void
     */
    public function registerViews()
    {
        $viewPath = resource_path('views/modules/verificationcode');

        $sourcePath = module_path('VerificationCode', 'Resources/views');

        $this->publishes([
            $sourcePath => $viewPath,
        ], 'views');

        $this->loadViewsFrom(array_merge(array_map(function ($path) {
            return $path . '/modules/verificationcode';
        }, \Config::get('view.paths')), [$sourcePath]), 'verificationcode');
    }

    /**
     * Register translations.
     *
     * @return void
     */
    public function registerTranslations()
    {
        $langPath = resource_path('lang/modules/verificationcode');

        if (is_dir($langPath)) {
            $this->loadTranslationsFrom($langPath, 'verificationcode');
        } else {
            $this->loadTranslationsFrom(module_path('VerificationCode', 'Resources/lang'), 'verificationcode');
        }
    }

    /**
     * Register an additional directory of factories.
     *
     * @return void
     */
    public function registerFactories()
    {
        if (!app()->environment('production') && $this->app->runningInConsole()) {
            app(Factory::class)->load(module_path('VerificationCode', 'Database/factories'));
        }
    }

    /**
     * Get the services provided by the provider.
     *
     * @return array
     */
    public function provides()
    {
        return [];
    }
}
