<?php

namespace Modules\VerificationCode\Support;

use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Notification;
use Modules\VerificationCode\Entities\VerificationCode;
use Modules\VerificationCode\Notifications\VerificationCodeCreated;
use Modules\VerificationCode\Notifications\VerificationCodeCreatedInterface;
use RuntimeException;

class VerificationCodeManager
{
    /**
     * Create and send a verification code.
     *
     * @param string $verifiable
     * @param string $channel
     *
     * @return void
     */
    public function send(string $verifiable, string $channel = 'mail')
    {
        if ($this->isTestVerifiable($verifiable)) {
            return;
        }

        $code = VerificationCode::createFor($verifiable);

        $notificationClass = $this->getNotificationClass();
        $notification = new $notificationClass($code, $verifiable);

        if ($notification instanceof ShouldQueue) {
            $notification->onQueue(config('verificationcode.queue', null));
        }

        Notification::route($channel, $verifiable)->notify($notification);
    }

    /**
     * Verify the code.
     *
     * @param string $code
     * @param string $verifiable
     *
     * @return bool
     */
    public function verify(string $code, string $verifiable)
    {
        if ($this->isTestVerifiable($verifiable)) {
            return $this->isTestCode($code);
        }

        $codeIsValid = VerificationCode::query()
            ->for($verifiable)
            ->notExpired()
            ->cursor()
            ->contains(function ($verificationCode) use ($code) {
                return Hash::check($code, $verificationCode->code);
            });

        if (!$codeIsValid) {
            return false;
        }

        VerificationCode::for($verifiable)->delete();

        return true;
    }

    /**
     * Check if the verifiable is a test verifiable.
     *
     * @param string $verifiable
     *
     * @return bool
     */
    protected function isTestVerifiable(string $verifiable)
    {
        $testVerifiables = config('verificationcode.test_verifiables', []);

        $testVerifiables = array_map(function ($email) {
            return strtolower($email);
        }, $testVerifiables);

        return in_array(strtolower($verifiable), $testVerifiables);
    }

    /**
     * Check if the code is the test code.
     *
     * @param string $code
     *
     * @return bool
     */
    protected function isTestCode(string $code)
    {
        if (empty(config('verificationcode.test_code'))) {
            return false;
        }

        return $code === config('verificationcode.test_code');
    }

    /**
     * Get the notification class.
     *
     * @throws RuntimeException
     *
     * @return string
     */
    protected function getNotificationClass()
    {
        $notificationClass = config('verificationcode.notification', VerificationCodeCreated::class);

        if (!is_subclass_of($notificationClass, VerificationCodeCreatedInterface::class)) {
            throw new RuntimeException('The notification class must implement the `\NextApps\VerificationCode\Notifications\VerificationCodeCreatedInterface` interface');
        }

        return $notificationClass;
    }
}
