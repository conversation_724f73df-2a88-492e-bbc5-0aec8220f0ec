<?php

namespace Modules\VerificationCode\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class VerificationCodeCreated extends Notification implements ShouldQueue, VerificationCodeCreatedInterface
{
    use Queueable;

    /**
     * @var string
     */
    public $code;

    /**
     * @var string
     */
    public $email;

    /**
     * Create a new message instance.
     *
     * @param string $code
     */
    public function __construct(string $code, string $email)
    {
        $this->code = $code;
        $this->email = $email;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array
     */
    public function via()
    {
        return ['mail'];
    }

    /**
     * Build the mail representation of the notification.
     *
     * @return MailMessage
     */
    public function toMail()
    {
        return (new MailMessage())
            ->from('<EMAIL>', 'TopDev')
            ->subject(__('[TopDev] Verification Code'))
            ->view('emails.verifi_notification_code', ['code' => $this->code, 'email'=> $this->email]);

    }
}
