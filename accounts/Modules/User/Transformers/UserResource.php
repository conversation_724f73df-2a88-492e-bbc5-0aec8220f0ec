<?php

namespace Modules\User\Transformers;

use Illuminate\Http\Resources\Json\JsonResource as Resource;

class UserResource extends Resource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->getKey(),
            'name' => $this->display_name,
            'email' => $this->email,
            'roles' => $this->roles(),
            'company_id' => $this->company_id,
            'approved' => $this->hasApprovedAccount(),
        ];
    }
}
