<?php

namespace Modules\User\Events;

use Illuminate\Queue\SerializesModels;
use Modules\User\Entities\User;

class NewUserProcessed
{
    use SerializesModels;

    /**
     * @var
     */
    private $user;

    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct(User $user)
    {
        $this->user = $user;
    }

    /**
     * Get user model instance.
     *
     * @return User
     */
    public function user()
    {
        return $this->user;
    }
}
