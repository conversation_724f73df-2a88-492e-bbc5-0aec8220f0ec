<?php

namespace Modules\User\Traits;

use Illuminate\Database\Eloquent\Builder;

trait MustApproveAccount
{
    /**
     * Boot the has approved trait for the model.
     *
     * @return void
     */
    public static function bootMustApproveAccount()
    {
    }

    /**
     * Determine if the user has Approved user.
     *
     * @return bool
     */
    public function hasApprovedAccount()
    {
        return !is_null($this->{static::APPROVED_AT});
    }

    /**
     * Mark the given user's email as verified.
     *
     * @return bool
     */
    public function markAccountAsApproved()
    {
        return $this->forceFill([
            static::APPROVED_AT => $this->freshTimestamp(),
        ])->save();
    }

    /**
     * Get the company that should be used for verification.
     *
     * @return \Modules\User\Entities\Company
     */
    public function getCompanyForVerification()
    {
        return $this->company;
    }

    /**
     *  Scope Approved.
     *
     * @return Illuminate\Database\Eloquent\Builder
     */
    public function scopeApproved(Builder $query)
    {
        return $query->whereNotNull(static::APPROVED_AT);
    }
}
