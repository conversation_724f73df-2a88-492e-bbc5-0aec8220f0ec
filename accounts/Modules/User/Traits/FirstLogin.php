<?php

namespace Modules\User\Traits;

/**
 * @deprecated first_login is defined as the first login per device
 */
trait FirstLogin
{
    /**
     * @var bool
     */
    protected $isFirstLogin;

    public static function bootFirstLogin()
    {
        static::created(function (self $user) {
            $user->setFirstLogin();
        });
    }

    public function setFirstLogin()
    {
        $this->isFirstLogin = true;
    }

    public function isFirstLogin()
    {
        return $this->isFirstLogin;
    }
}
