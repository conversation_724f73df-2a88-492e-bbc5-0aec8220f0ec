<?php

namespace Modules\User\Entities;

use Illuminate\Database\Eloquent\Model;

class Social extends Model
{
    /**
     * @inheritdoc
     */
    protected $connection = 'mysql';

    protected $table = 'socials';

    protected $fillable = [
        'user_id', 'provider_user_id', 'provider', 'name', 'email', 'avatar',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
