# API Endpoints - Accounts Project

## 🚀 **Đã thêm API endpoints vào source accounts**

### **Base URL**: `https://accounts.topdev.vn/api`

## 📋 **Authentication Endpoints**

### **1. Test API**
```http
GET /api/test
```
**Response:**
```json
{
    "success": true,
    "message": "API is working!",
    "timestamp": "2025-08-04T10:30:00.000000Z",
    "version": "1.0.0"
}
```

### **2. Register User**
```http
POST /api/auth/register
Content-Type: application/json

{
    "email": "<EMAIL>",
    "password": "password123",
    "password_confirmation": "password123",
    "display_name": "<PERSON>",
    "type": "resume"
}
```

**Response:**
```json
{
    "success": true,
    "message": "User registered successfully",
    "data": {
        "user": {
            "id": "uuid-here",
            "email": "<EMAIL>",
            "display_name": "<PERSON>",
            "type": "resume"
        },
        "access_token": "token-here",
        "token_type": "Bearer"
    }
}
```

### **3. Login User**
```http
POST /api/auth/login
Content-Type: application/json

{
    "email": "<EMAIL>",
    "password": "password123"
}
```

**Response:**
```json
{
    "success": true,
    "message": "Login successful",
    "data": {
        "user": {
            "id": "uuid-here",
            "email": "<EMAIL>",
            "display_name": "John Doe",
            "type": "resume"
        },
        "access_token": "token-here",
        "token_type": "Bearer"
    }
}
```

### **4. Get Current User**
```http
GET /api/auth/me
Authorization: Bearer {access_token}
```

**Response:**
```json
{
    "success": true,
    "data": {
        "user": {
            "id": "uuid-here",
            "email": "<EMAIL>",
            "display_name": "John Doe",
            "type": "resume",
            "email_verified_at": "2025-08-04T10:30:00.000000Z",
            "created_at": "2025-08-04T10:30:00.000000Z",
            "updated_at": "2025-08-04T10:30:00.000000Z"
        }
    }
}
```

### **5. Logout User**
```http
POST /api/auth/logout
Authorization: Bearer {access_token}
```

**Response:**
```json
{
    "success": true,
    "message": "Logged out successfully"
}
```

## 🔗 **Social Authentication Endpoints**

### **6. Get Social Login URL**
```http
GET /api/auth/social/{provider}
```
**Providers**: `facebook`, `twitter`, `google`, `linkedin`, `github`, `apple`

**Response:**
```json
{
    "success": true,
    "message": "Redirect URL generated successfully",
    "data": {
        "redirect_url": "https://www.facebook.com/v18.0/dialog/oauth?..."
    }
}
```

### **7. Handle Social Callback**
```http
POST /api/auth/social/{provider}/callback
Content-Type: application/json

{
    "code": "authorization_code_from_provider",
    "state": "state_parameter"
}
```

**Response:**
```json
{
    "success": true,
    "message": "Social login successful",
    "data": {
        "user": {
            "id": "uuid-here",
            "email": "<EMAIL>",
            "display_name": "John Doe",
            "type": "resume"
        },
        "access_token": "token-here",
        "token_type": "Bearer",
        "provider": "facebook"
    }
}
```

### **8. Get Linked Social Accounts**
```http
GET /api/auth/social/accounts
Authorization: Bearer {access_token}
```

**Response:**
```json
{
    "success": true,
    "data": {
        "linked_accounts": [
            {
                "provider": "facebook",
                "provider_user_id": "*********",
                "name": "John Doe",
                "email": "<EMAIL>",
                "linked_at": "2025-08-04T10:30:00.000000Z"
            }
        ]
    }
}
```

### **9. Link Social Account**
```http
POST /api/auth/social/{provider}/link
Authorization: Bearer {access_token}
Content-Type: application/json

{
    "access_token": "social_provider_access_token"
}
```

**Response:**
```json
{
    "success": true,
    "message": "Social account linked successfully"
}
```

### **10. Unlink Social Account**
```http
DELETE /api/auth/social/{provider}/unlink
Authorization: Bearer {access_token}
```

**Response:**
```json
{
    "success": true,
    "message": "Social account unlinked successfully"
}
```

## 🔄 **OAuth2 Token Refresh**

### **11. Refresh Token**
```http
POST /oauth/token
Content-Type: application/json

{
    "grant_type": "refresh_token",
    "refresh_token": "your_refresh_token",
    "client_id": "your_client_id",
    "client_secret": "your_client_secret"
}
```

**Response:**
```json
{
    "token_type": "Bearer",
    "expires_in": ********,
    "access_token": "new_access_token",
    "refresh_token": "new_refresh_token"
}
```

## 🛠️ **Setup Instructions**

### **1. Run Migration**
```bash
cd accounts
php artisan migrate
```

### **2. Install Passport (if not already done)**
```bash
php artisan passport:install
```

### **3. Configure Social Providers**
Update `.env` file:
```env
# Facebook
FACEBOOK_CLIENT_ID=your_facebook_app_id
FACEBOOK_CLIENT_SECRET=your_facebook_app_secret
FACEBOOK_REDIRECT_URI=https://accounts.topdev.vn/login/facebook/callback

# Google
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
GOOGLE_REDIRECT_URI=https://accounts.topdev.vn/login/google/callback

# Twitter
TWITTER_CLIENT_ID=your_twitter_client_id
TWITTER_CLIENT_SECRET=your_twitter_client_secret
TWITTER_REDIRECT_URI=https://accounts.topdev.vn/login/twitter/callback
```

### **4. Test API**
```bash
curl -X GET https://accounts.topdev.vn/api/test
```

## 🎯 **Features**

✅ **Sử dụng toàn bộ logic có sẵn của accounts**
✅ **Event system hoàn chỉnh** (Login, Logout, Register events)
✅ **Social login** với tất cả providers
✅ **Token management** với Passport
✅ **User freezing** check
✅ **Database relationships** (User -> Social)
✅ **Error handling** comprehensive
✅ **JSON API responses** chuẩn

## 🔧 **Architecture**

- **Controllers**: Sử dụng logic từ `LoginController`, `RegisterController`
- **Models**: Sử dụng `User`, `Social` models có sẵn
- **Events**: Sử dụng event system có sẵn
- **Jobs**: Sử dụng queue system có sẵn
- **Database**: Sử dụng database structure có sẵn

**✅ API endpoints đã được thêm vào accounts project và sẵn sàng sử dụng!**
