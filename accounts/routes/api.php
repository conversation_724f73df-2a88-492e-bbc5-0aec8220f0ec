<?php

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::post('changepassword', 'Auth\ResetPasswordController@changePassword')->middleware('auth:api');

Route::get('v1/user/request-delete', 'Auth\DeleteController@magicLink')->middleware('auth:api');

/**
 * Tracking activity for signup event which blocks by adblock
 */
Route::post('v1/activity', [App\Http\Controllers\Api\ActivityController::class, 'tracking'])
    ->name('api.v1.activity');

/*
|--------------------------------------------------------------------------
| Authentication API Routes
|--------------------------------------------------------------------------
*/

// Public authentication routes
Route::prefix('auth')->group(function () {
    Route::post('/register', 'Api\AuthController@register');
    Route::post('/login', 'Api\AuthController@login');
    Route::post('/refresh', 'Api\AuthController@refresh');

    // Social authentication
    Route::get('/social/{provider}', 'Api\SocialAuthController@redirectToProvider');
    Route::post('/social/{provider}/callback', 'Api\SocialAuthController@handleProviderCallback');
});

// Protected authentication routes
Route::middleware('auth:api')->group(function () {
    Route::prefix('auth')->group(function () {
        Route::get('/me', 'Api\AuthController@me');
        Route::post('/logout', 'Api\AuthController@logout');

        // Social account management
        Route::get('/social/accounts', 'Api\SocialAuthController@getLinkedAccounts');
        Route::post('/social/{provider}/link', 'Api\SocialAuthController@linkAccount');
        Route::delete('/social/{provider}/unlink', 'Api\SocialAuthController@unlinkAccount');
    });
});

// Test endpoint
Route::get('/test', function () {
    return response()->json([
        'success' => true,
        'message' => 'API is working!',
        'timestamp' => now(),
        'version' => '1.0.0'
    ]);
});
