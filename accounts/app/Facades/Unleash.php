<?php

namespace App\Facades;

use Illuminate\Support\Facades\Facade;

/**
 * @method static bool isEnabled(string $featureName, ?Context $context = null, bool $default = false)
 *
 * @see Unleash\Client\Unleash
 */
class Unleash extends Facade
{
    protected static function getFacadeAccessor(): string
    {
        return \Unleash\Client\Unleash::class;
    }

    public static function isEnabled(string $featureName, ?Context $context = null, bool $default = false)
    {
        return true;
    }
}
