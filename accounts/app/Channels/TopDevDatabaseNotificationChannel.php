<?php

namespace App\Channels;

use Illuminate\Notifications\Notification;
use Illuminate\Foundation\Bus\DispatchesJobs;
use App\Jobs\TopDevDatabaseNotification;

class TopDevDatabaseNotificationChannel
{
    use DispatchesJobs;

    /**
     * Send the given notification.
     *
     * @param  mixed  $notifiable
     * @param  \Illuminate\Notifications\Notification  $notification
     * @return void
     */
    public function send($notifiable, Notification $notification)
    {
        $messages = $notification->toTopDevDatabase($notifiable);

        TopDevDatabaseNotification::dispatch($notifiable->getKey(), $messages, get_class($notification));
    }
}
