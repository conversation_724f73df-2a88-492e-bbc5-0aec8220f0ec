<?php

namespace App\Channels\Messages;

class SendMailMessage
{
    /**
    * Email người nhận
    *
    * @var string
    */
    public $to;

    /**
    * List email người được cc.
    *
    * @var string
    */
    public $cc;

    /**
    * Email người gửi
    *
    * @var string
    */
    public $from;

    /**
    * Tên người gửi
    *
    * @var string
    */
    public $fromName;

    /**
    * Tiêu đề email
    *
    * @var string
    */
    public $subject;

    /**
    * Nội dung tin nhắn
    *
    * @var string
    */
    public $body;

    /**
     * delay gửi mail
     * @var int
     */
    public $delay;

    /**
    * attachs
    *
    * @var array
    */
    public $attachs;

    /**
     * Create a new message instance.
     *
     * @param  string  $content
     * @return void
     */
    public function __construct($body = '')
    {
        $this->body = $body;
    }

    public function to($to)
    {
        $this->to = $to;

        return $this;
    }

    public function cc($cc)
    {
        $this->cc = $cc;

        return $this;
    }

    public function from($from)
    {
        $this->from = $from;

        return $this;
    }

    public function fromName($name)
    {
        $this->fromName = $name;

        return $this;
    }

    public function subject($subject)
    {
        $this->subject = $subject;

        return $this;
    }

    public function body($body)
    {
        $this->body = $body;

        return $this;
    }

    public function delay($delay)
    {
        $this->delay = $delay;

        return $this;
    }

    public function attachs($attachs)
    {
        $this->attachs = $attachs;

        return $this;
    }
}
