<?php

namespace App\Channels;

use Illuminate\Notifications\Notification;
use Illuminate\Foundation\Bus\DispatchesJobs;
use App\Jobs\SendMail;
use App\Channels\Messages\SendMailMessage;

class SendMailChannel
{
    use DispatchesJobs;

    /**
     * Send the given notification.
     *
     * @param  mixed  $notifiable
     * @param  \Illuminate\Notifications\Notification  $notification
     * @return void
     */
    public function send($notifiable, Notification $notification)
    {
        $message = $notification->toSendMail($notifiable);

        if (is_string($message)) {
            $message = new SendMailMessage($message);
        }

        $params_sendmail = [
            'to'        => $message->to,
            'cc'        => $message->cc,
            'fromName'  => $message->fromName,
            'from'      => $message->from,
            'subject'   => $message->subject,
            'body'      => $message->body,
            'attachs'   => $message->attachs
        ];

        $command = (new SendMail($params_sendmail));

        if ($message->delay > 0) {
            $command->delay(now()->addSeconds($message->delay));
        }

        dispatch($command);
    }
}
