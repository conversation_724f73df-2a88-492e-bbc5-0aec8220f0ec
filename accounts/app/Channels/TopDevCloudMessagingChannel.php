<?php

namespace App\Channels;

use Illuminate\Notifications\Notification;
use Illuminate\Foundation\Bus\DispatchesJobs;
use App\Jobs\TopDevCloudMessaging;

class TopDevCloudMessagingChannel
{
    use DispatchesJobs;

    /**
     * Send the given notification.
     *
     * @param  mixed  $notifiable
     * @param  \Illuminate\Notifications\Notification  $notification
     * @return void
     */
    public function send($notifiable, Notification $notification)
    {
        $messages = $notification->toTopDevFcm($notifiable);
        $dataDestination = $notification->setDataDestination($notifiable);

        TopDevCloudMessaging::dispatch(
            $notifiable->getKey(),
            $notification->toFcmDeviceToken($notifiable),
            $messages,
            $dataDestination
        );
    }
}
