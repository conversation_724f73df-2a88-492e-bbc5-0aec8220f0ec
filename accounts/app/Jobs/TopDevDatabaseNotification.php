<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Support\Facades\Log;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;

class TopDevDatabaseNotification implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    protected $user;

    protected $messages;

    protected $notification;

    /**
     * Create a new job instance.
     *
     * @param $payload
     */
    public function __construct($user, $messages, $notification)
    {
        $this->user = $user;
        $this->messages = $messages;
        $this->notification = $notification;
        Log::info('TopDevDatabaseNotification ' . $user);
        Log::info($messages);

        $this->onConnection(config('queue.ams'));
    }
}
