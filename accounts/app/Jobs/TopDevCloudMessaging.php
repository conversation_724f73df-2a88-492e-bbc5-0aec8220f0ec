<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Support\Facades\Log;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;

class TopDevCloudMessaging implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    protected $user;

    protected $token;

    protected $messages;

    protected $data;

    /**
     * Create a new job instance.
     *
     * @param $payload
     */
    public function __construct($user, $token, $messages, $data)
    {
        $this->user = $user;
        $this->token = $token;
        $this->messages = $messages;
        $this->data = $data;
        Log::info('TopDevCloudMessaging ' . $user);
        Log::info($messages);

        $this->onConnection(config('queue.ams'));
    }
}
