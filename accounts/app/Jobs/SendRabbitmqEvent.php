<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Symfony\Component\Translation\Exception\RuntimeException;

class SendRabbitmqEvent implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    private mixed $data;

    private string $origin;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($data, $origin = 'ACCOUNTS_TOPDEV')
    {
        $this->data = $data;
        $this->origin = $origin;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        throw new RuntimeException("Handle in AMS please");
    }
}
