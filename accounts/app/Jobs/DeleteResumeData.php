<?php

namespace App\Jobs;

use GuzzleHttp\Client;
use Illuminate\Bus\Queueable;
use Modules\User\Entities\User;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use Illuminate\Queue\SerializesModels;
use Illuminate\Queue\InteractsWithQueue;
use Modules\AuthenLog\Entities\AuthenLog;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;

class DeleteResumeData implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $userId;
    public $tries = 3;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($userId)
    {
        $this->userId = $userId;
        Log::info('-------------------- Start DeleteResumeData ' . $userId . ' --------------------');
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $userId = $this->userId;
        $token = User::whereEmail(config('app.email_admin'))
            ->first()
            ->createToken('TokenAdmin')
            ->accessToken;

        if ($userId && $token) {
            $user = User::find($userId);

            $authenLog = AuthenLog::where('authenticatable_id', $userId);
            $authenLog->get()->unsearchable();
            $authenLog->delete();

            $responseAms = $this->callApi(api_ams_url('delete-resume/' . $userId), $token);
            $responseCv = $this->callApi(api_cv_url('delete-resume/' . $userId), $token);
            $responsePt = $this->callApi(api_pt_url('delete-resume/' . $userId), $token);

            if ($responseCv->message == 'success' && $responsePt->message == 'success' && $responseAms->message == 'success') {
                $user->freeze_at = null;
                $user->save();
                $user->delete();
                Log::info('Success !!!');
            }
        }
        Log::info('-------------------- End DeleteResumeData ' . $userId . ' --------------------');
    }

    public function callApi($url, $token)
    {
        $client = new Client();
        $response = $client->request(
            'DELETE',
            $url,
            [
                'headers' => ['Authorization' => 'Bearer ' . $token]
            ]
        );

        return json_decode($response->getBody()->getContents());
    }
}
