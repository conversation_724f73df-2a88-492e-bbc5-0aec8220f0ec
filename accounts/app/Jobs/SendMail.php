<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Telegram\Bot\Api;
use Mail;
use Storage;
use Exception;

class SendMail implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    private $params;
    private $type;
    private $to;
    private $subject;
    private $body       = '';
    private $from       = '<EMAIL>';
    private $fromName   = 'TopDev';
    private $cc         = '';
    private $bcc        = '';
    private $replyTo    = '';
    private $priority   = '';
    private $attachs    = '';

    /**
     *
     * @var Telegram\Bot\Api;
     */
    private $telegram;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 3;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($params)
    {
        $this->params   = $params;
        $this->to       = isset($params['to']) ? $params['to'] : null;
        $this->subject  = isset($params['subject']) ? $params['subject'] : null;
        $this->body     = isset($params['body']) ? $params['body'] : null;
        $this->from     = isset($params['from']) ? $params['from'] : null;
        $this->fromName = isset($params['fromName']) ? $params['fromName'] : 'TopDev';
        $this->cc       = isset($params['cc']) ? $params['cc'] : null;
        $this->bcc      = isset($params['bcc']) ? $params['bcc'] : null;
        $this->replyTo  = isset($params['replyTo']) ? $params['replyTo'] : null;
        $this->attachs  = isset($params['attachs']) ? $params['attachs'] : [];
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        Mail::send('emails.blank', ['html' => $this->body], function ($message) {
            $message->from($this->from, $this->fromName);
            $message->to($this->to);
            $message->subject($this->subject);
            // $message->sender($address, $name = null);

            if (!empty($this->cc)) {
                $message->cc($this->cc);
            }

            if (!empty($this->bcc)) {
                $message->bcc($this->bcc);
            }

            if (!empty($this->replyTo)) {
                $message->replyTo($this->replyTo);
            }

            // $message->priority($level);

            if (!empty($this->attachs)) {
                $options = [];
                if (isset($this->attachs['display'])) {
                    $options['as'] = 'TopDevCv - ' . $this->attachs['display'];
                }

                if (isset($this->attachs['mime'])) {
                    $options['mime'] = $this->attachs['mime'];
                }

                $message->attach($this->attachs['pathToFile'], $options);
            }

            $message->sdt = '0912809264';

            // Get the underlying SwiftMailer message instance...
            // $message->getSwiftMessage();
        });
    }

    /**
     * The job failed to process.
     *
     * @param  Exception  $exception
     * @return void
     */
    public function failed(Exception $exception)
    {
        $telegram = new Api(env('TELEGRAM_BOT_TOKEN'));
        $telegram->sendMessage([
            'chat_id'   => \App::environment('production') ? '-262974614' : env('CHAT_ID'),
            'text'      => "Sendmail failed: ". $this->to . "\n@sonnx",
            // 'parse_mode'=> 'Markdown'
        ]);
    }
}
