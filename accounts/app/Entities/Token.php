<?php

namespace App\Entities;

use <PERSON><PERSON>\Passport\Token as BaseToken;
use App\Entities\RefreshToken;
use GeneaLabs\LaravelModelCaching\Traits\Cachable;
use Illuminate\Database\Eloquent\Builder;

class Token extends BaseToken
{
    //use Cachable;

    public const STATUS_ALIVE = 0;
    public const STATUS_REVOKED = 1;

    /**
     * The connection name for the model.
     *
     * @var string
     */
    protected $connection = 'mysql';

    protected $cachePrefix = "token";

    protected $cacheCooldownSeconds = 300;

    /**
     * Determine if the client should skip the authorization prompt.
     *
     * @return bool
     */
    public function refreshToken()
    {
        return $this->hasOne(RefreshToken::class, 'access_token_id');
    }

    public function clientPasswordNullable()
    {
        return $this->client->passwordCanNullable();
    }

    public function scopeAlive(Builder $builder)
    {
        return $builder->where('revoked', self::STATUS_ALIVE);
    }
}
