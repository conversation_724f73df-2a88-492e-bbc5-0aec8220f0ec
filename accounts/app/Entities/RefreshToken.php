<?php

namespace App\Entities;

use Illuminate\Database\Eloquent\Model;
use Laravel\Passport\Passport;
use GeneaLabs\LaravelModelCaching\Traits\Cachable;

class RefreshToken extends Model
{
    //use Cachable;

    /**
     * The connection name for the model.
     *
     * @var string
     */
    protected $connection = 'mysql';

    protected $cachePrefix = "refresh-token";

    protected $cacheCooldownSeconds = 300;

    /**
     * The database table used by the model.
     *
     * @var string
     */
    protected $table = 'oauth_refresh_tokens';

    /**
     * Indicates if the IDs are auto-incrementing.
     *
     * @var bool
     */
    public $incrementing = false;

    /**
     * The "type" of the primary key ID.
     *
     * @var string
     */
    protected $keyType = 'string';

    /**
     * The guarded attributes on the model.
     *
     * @var array
     */
    protected $guarded = [];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'revoked' => 'bool', 'expires_at' => 'datetime',
    ];

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;

    /**
     * Get all of the authentication codes for the client.
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function accessToken()
    {
        return $this->belongsTo(Passport::tokenModel(), 'access_token_id');
    }

    /**
     * Revoke the refresh token instance.
     *
     * @return bool
     */
    public function revoke()
    {
        return $this->forceFill(['revoked' => true])->save();
    }
}
