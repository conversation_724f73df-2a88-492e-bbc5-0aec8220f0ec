<?php

namespace App\Entities;

use <PERSON><PERSON>\Passport\PersonalAccessClient as BasePersonalAccessClient;
use GeneaLabs\LaravelModelCaching\Traits\Cachable;

class PersonalAccessClient extends BasePersonalAccessClient
{
    //use Cachable;

    /**
     * The connection name for the model.
     *
     * @var string
     */
    protected $connection = 'mysql';

    protected $cachePrefix = "personal-access-client";

    protected $cacheCooldownSeconds = 300;
}
