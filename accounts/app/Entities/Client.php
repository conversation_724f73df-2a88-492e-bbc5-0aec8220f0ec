<?php

namespace App\Entities;

use <PERSON><PERSON>\Passport\Client as BaseClient;
use GeneaLabs\LaravelModelCaching\Traits\Cachable;

class Client extends BaseClient
{
    //use Cachable;

    public const PASSWORD_NULLABLE = 1;
    public const PASSWORD_NOT_NULLABLE = 0;

    /**
     * The connection name for the model.
     *
     * @var string
     */
    protected $connection = 'mysql';

    protected $cachePrefix = "client";

    protected $cacheCooldownSeconds = 300;

    /**
     * Determine if the client should skip the authorization prompt.
     *
     * @return bool
     */
    public function skipsAuthorization()
    {
        return true;//$this->firstParty();
    }

    /**
     * If the client set nullable social password, when create user,
     * you doesn't need update password.
     *
     * @return bool
     */
    public function passwordCanNullable()
    {
        return $this->password_social_nullable == self::PASSWORD_NULLABLE;
    }
}
