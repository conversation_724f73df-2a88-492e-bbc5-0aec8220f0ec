<?php

namespace App\Exceptions;

use Throwable;
use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use League\OAuth2\Server\Exception\OAuthServerException;

class Handler extends ExceptionHandler
{
    /**
     * A list of the exception types that are not reported.
     *
     * @var array
     */
    protected $dontReport = [];

    /**
     * A list of the inputs that are never flashed for validation exceptions.
     *
     * @var array
     */
    protected $dontFlash = [
        'password',
        'password_confirmation',
    ];

    /**
     * Report or log an exception.
     *
     * @param  \Throwable  $exception
     * @return void
     */
    public function report(Throwable $exception)
    {
        if ($exception instanceof OAuthServerException && in_array($exception->getCode(), [3, 4, 8, 9])) {
            return;
        }

        if (app()->environment('production') && app()->bound('sentry') && $this->shouldReport($exception)) {
            app('sentry')->captureException($exception);
        }

        parent::report($exception);
    }

    /**
     * Render an exception into an HTTP response.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Throwable  $exception
     * @return \Illuminate\Http\Response
     */
    public function render($request, Throwable $exception)
    {
        $error50x = array(500, 501, 502, 503, 504, 505);
        $error40X = array(400, 401, 402, 403, 404, 405);
        if ($this->isHttpException($exception)) {
            if (in_array($exception->getStatusCode(), $error40X)) {
                return redirect()->route('page404');
            }

            if (in_array($exception->getStatusCode(), $error50x)) {
                return redirect()->route('page500');
            }
        }

        return parent::render($request, $exception);
    }
}
