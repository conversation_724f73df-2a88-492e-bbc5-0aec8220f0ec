<?php

namespace App\Listeners;

use <PERSON><PERSON>\Passport\Events\AccessTokenCreated;

class RevokeOldTokens
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  Laravel\Passport\Events\AccessTokenCreated  $accessToken
     * @return void
     */
    public function handle(AccessTokenCreated $accessToken)
    {
    }
}
