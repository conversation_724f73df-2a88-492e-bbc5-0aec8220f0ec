<?php

namespace App\Listeners;

use Illuminate\Http\Request;
use Illuminate\Auth\Events\Logout;
use Illuminate\Support\Facades\Log;
use App\Jobs\CreateFcmDeviceTokenActivity;

class LogSuccessfulLogoutWebDevice
{
    protected $request;

    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct(Request $request)
    {
        $this->request = $request;
    }

    /**
     * Handle the event.
     *
     * @param  \Illuminate\Auth\Events\Logout  $logout
     * @return void
     */
    public function handle(Logout $logout)
    {
        if ($logout->guard === 'web') {
            Log::info('LogSuccessfulLogoutWebDevice: ' . $logout->user->getKey());
            $user = $logout->user;

            $fcmToken = $this->request->cookie('topdev-device-token-web') ?? null;
            if (!empty($fcmToken)) {
                Log::info('User [' . $user->email . '] logout with fcm token from Web with device token [' . $fcmToken. ']');
                CreateFcmDeviceTokenActivity::dispatch($user->getKey(), [
                    'action' => 'logout',
                    'fcm_device_token' => $fcmToken,
                    'source' => 'Web'
                ]);
            } else {
                Log::info('Empty LogoutWebDevice: ' . $logout->user->getKey());
            }
        }
    }
}
