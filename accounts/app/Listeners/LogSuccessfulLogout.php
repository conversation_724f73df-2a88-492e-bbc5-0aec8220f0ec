<?php

namespace App\Listeners;

use Illuminate\Auth\Events\Logout;
use Illuminate\Support\Facades\Log;
use Illuminate\Contracts\Queue\ShouldQueue;

class LogSuccessfulLogout// implements ShouldQueue
{
    /**
     * Handle the event.
     *
     * @param  \Illuminate\Auth\Events\Logout  $logout
     * @return void
     */
    public function handle(Logout $logout)
    {
        Log::info('Starting revoke token user: [' . $logout->user->email . '] #' . $logout->user->getKey());

        $tokens = $logout->user->aliveTokens;
        $tokens = $this->tokensShouldBeRevoking($logout, $tokens);

        foreach ($tokens as $key => $token) {
            $token->revoke();
            Log::info('Token ID has revoked: ' . $token->getKey());
            if ($token->refreshToken) {
                $token->refreshToken->revoke();
            }
        }
    }

    protected function tokensShouldBeRevoking(Logout $logout, $tokens)
    {
        if ($this->isGuardWeb($logout) && !$this->userIsFreezing($logout)) {
            $tokens = $tokens->whereNotIn(
                'client_id',
                [config('authenlog.client_mobile')]
            );
        }

        return $tokens;
    }

    //Nếu user logout bằng guard web thì sẽ không revoke token của mobile
    protected function isGuardWeb(Logout $logout)
    {
        return $logout->guard === 'web';
    }

    //Nếu user đang freezing thì xóa hết token lun nha
    protected function userIsFreezing(Logout $logout)
    {
        return $logout->user->isFreezing($logout);
    }

}
