<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class ActivityController extends Controller
{
    public function tracking(Request $request)
    {
        if ($request->input('user_id') && $request->input('activity_id')) {
            if (Cache::pull('trackingView-' . $request->input('user_id'), 'NOTFOUND') === $request->input('activity_id')) {
                Log::info('User activity tracking ('. $request->input('event') .'): ' . $request->input('user_id'));
            }
        }

        return response()->json([], 200);
    }
}
