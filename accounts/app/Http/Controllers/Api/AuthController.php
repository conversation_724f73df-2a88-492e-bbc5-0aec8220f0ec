<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\ValidationException;
use Modules\User\Entities\User;
use Modules\User\Events\NewUserProcessed;
use Modules\User\Events\SignUp;
use Laravel\Passport\TokenRepository;
use Laravel\Passport\RefreshTokenRepository;

class AuthController extends Controller
{
    protected $tokenRepository;
    protected $refreshTokenRepository;

    public function __construct(
        TokenRepository $tokenRepository,
        RefreshTokenRepository $refreshTokenRepository
    ) {
        $this->tokenRepository = $tokenRepository;
        $this->refreshTokenRepository = $refreshTokenRepository;
    }

    /**
     * Register a new user
     */
    public function register(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email|unique:users,email',
            'password' => 'required|min:6|confirmed',
            'display_name' => 'required|string|max:255',
            'type' => 'required|in:resume,employer',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        // Create user
        $user = User::create([
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'display_name' => $request->display_name,
            'type' => $request->type,
        ]);

        // Fire events (sử dụng logic có sẵn của accounts)
        event(new \Illuminate\Auth\Events\Registered($user));
        event(new NewUserProcessed($user));
        event(new SignUp($user, [
            'method' => 'email_password',
            'user_agent' => $request->userAgent(),
            'ip_address' => $request->ip(),
        ]));

        // Create token
        $token = $user->createToken('API Token')->accessToken;

        return response()->json([
            'success' => true,
            'message' => 'User registered successfully',
            'data' => [
                'user' => [
                    'id' => $user->id,
                    'email' => $user->email,
                    'display_name' => $user->display_name,
                    'type' => $user->type,
                ],
                'access_token' => $token,
                'token_type' => 'Bearer',
            ]
        ], 201);
    }

    /**
     * Login user
     */
    public function login(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
            'password' => 'required',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        // Attempt login (sử dụng logic có sẵn)
        if (!Auth::attempt($request->only('email', 'password'))) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid credentials'
            ], 401);
        }

        $user = Auth::user();

        // Check if user is frozen
        if ($user->isFreezing()) {
            return response()->json([
                'success' => false,
                'message' => 'Account is frozen'
            ], 403);
        }

        // Create token
        $token = $user->createToken('API Token')->accessToken;

        // Fire login event (sử dụng event system có sẵn)
        event(new \Illuminate\Auth\Events\Login('api', $user, false));

        return response()->json([
            'success' => true,
            'message' => 'Login successful',
            'data' => [
                'user' => [
                    'id' => $user->id,
                    'email' => $user->email,
                    'display_name' => $user->display_name,
                    'type' => $user->type,
                ],
                'access_token' => $token,
                'token_type' => 'Bearer',
            ]
        ]);
    }

    /**
     * Get current user
     */
    public function me(Request $request)
    {
        $user = $request->user();

        return response()->json([
            'success' => true,
            'data' => [
                'user' => [
                    'id' => $user->id,
                    'email' => $user->email,
                    'display_name' => $user->display_name,
                    'type' => $user->type,
                    'email_verified_at' => $user->email_verified_at,
                    'created_at' => $user->created_at,
                    'updated_at' => $user->updated_at,
                ]
            ]
        ]);
    }

    /**
     * Logout user
     */
    public function logout(Request $request)
    {
        $user = $request->user();
        
        // Fire logout event (sử dụng event system có sẵn)
        event(new \Illuminate\Auth\Events\Logout('api', $user));

        // Revoke current token
        $token = $request->user()->token();
        if ($token) {
            $this->tokenRepository->revokeAccessToken($token->id);
            
            // Revoke refresh tokens
            $this->refreshTokenRepository->revokeRefreshTokensByAccessTokenId($token->id);
        }

        return response()->json([
            'success' => true,
            'message' => 'Logged out successfully'
        ]);
    }

    /**
     * Refresh token
     */
    public function refresh(Request $request)
    {
        // This will be handled by Laravel Passport's built-in refresh endpoint
        // Just return info about how to use it
        return response()->json([
            'success' => true,
            'message' => 'Use POST /oauth/token with grant_type=refresh_token',
            'endpoint' => url('/oauth/token'),
            'method' => 'POST',
            'body' => [
                'grant_type' => 'refresh_token',
                'refresh_token' => 'your_refresh_token',
                'client_id' => 'your_client_id',
                'client_secret' => 'your_client_secret',
            ]
        ]);
    }
}
