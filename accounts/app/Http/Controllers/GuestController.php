<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Redmix0901\Core\Http\Responses\BaseHttpResponse;
use Illuminate\Support\Facades\Cookie;
use Log;

class GuestController extends Controller
{
    /**
     * Check auth via cookie.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function checkCookie(Request $request, BaseHttpResponse $response)
    {
        if (empty($request->user())) {
            return response()->json(false);
        }

        return response()->json(true);
    }
    public function lang($locale)
    {
        app()->setLocale($locale);
        session()->put('topdev_locale', $locale);
        Cookie::queue('topdev_locale', $locale);

        return redirect()->back();
    }

    public function page404(Request $request)
    {
        return view('layouts.page-400');
    }

    public function page500()
    {
        return view('layouts.page-500');
    }

    public function brokenLink()
    {
        return view('layouts.broken-link');
    }

    public function login()
    {
        return redirect('/');
    }

    public function register()
    {
        return redirect('/');
    }
}
