<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Http\Requests\ShowFormLoginRequest;
use Carbon\Carbon;
use Exception;
use Illuminate\Auth\Passwords\PasswordBroker;
use Illuminate\Contracts\Config\Repository as Config;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\View\Factory;
use Illuminate\Foundation\Auth\AuthenticatesUsers;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Crypt;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;
use Illuminate\Validation\ValidationException;
use Illuminate\View\View;
use Jenssegers\Agent\Agent;
use Laravel\Socialite\Facades\Socialite;
use Modules\User\Entities\User;
use Modules\User\Entities\UserMainCv;
use Modules\User\Entities\UserProfile;
use Modules\User\Repositories\Contracts\UserRepositoryInterface;
use Redmix0901\Core\Http\Responses\BaseHttpResponse;
use Spatie\GoogleTagManager\GoogleTagManagerFacade as GoogleTagManager;
use Symfony\Component\HttpFoundation\Cookie;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Cookie as LaravelCookie;
use Modules\User\Entities\UserCollectEvent;

class LoginController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Login Controller
    |--------------------------------------------------------------------------
    |
    | This controller handles authenticating users for the application and
    | redirecting them to your home screen. The controller uses a trait
    | to conveniently provide its functionality to your applications.
    |
    */

    use AuthenticatesUsers;

    /**
     * Where to redirect users after login.
     *
     * @var string
     */
    protected $redirectTo = '/';

    /**
     * The configuration repository implementation.
     *
     * @var \Illuminate\Contracts\Config\Repository
     */
    protected $config;
    /**
     * @var PasswordBroker
     */
    protected $broker;

    /**
     * @var UserRepositoryInterface
     */
    protected $userRepository;

    /**
     * @var string
     */
    protected $cookieName = 'TDUID';

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct(
        UserRepositoryInterface $user,
        PasswordBroker          $broker,
        Config                  $config
    )
    {
        $this->middleware('guest')->except('logout');
        $this->broker = $broker;
        $this->userRepository = $user;
        $this->config = $config;
    }

    /**
     *  Show the application's login form.
     *
     * @param ShowFormLoginRequest $request
     *
     * @return Application|Factory|View
     */
    public function showLoginForm(ShowFormLoginRequest $request)
    {
        $agent = new Agent();
        GoogleTagManager::set('pageType', 'accountLogin');
        if ($request->isFromViewStatusApply()) {
            $login_view = ShowFormLoginRequest::FLAG_LOGIN_TO_VIEW_STATUS_APPLY;
        } elseif ($request->isFromBlog()) {
            $login_view = ShowFormLoginRequest::FLAG_TOPDEV_BLOG;
        } else if ($request->isFromViewDashCandidate()) {
            $login_view = ShowFormLoginRequest::FLAG_LOGIN_TO_VIEW_EMPLOYER_DASH_CANDIDATE;
        }

        if ($request->has('redirect_uri')) {
            session()->put('redirect_uri', $request->get('redirect_uri'));
        }

        if ($request->has('referring_name')) {
            session()->put('referring_name', $request->get('referring_name'));
            $referringCookie = cookie('referring_name', $request->get('referring_name'), 60 * 24 * 30, null, null, null, false);

            cookie()->queue($referringCookie);
        }
        $this->checkLoginRererer($request);
        if ($request->has('referring_from')) {
            session()->put('referring_from', $request->get('referring_from'));
        }
        return view('auth.login-page', [
            'agent' => $agent,
            'login_view' => $login_view ?? '',
        ]);
    }

    /**
     * Redirect the user to the $provider authentication page.
     *
     * @param $provider
     *
     * @return RedirectResponse|\Symfony\Component\HttpFoundation\RedirectResponse
     */
    public function redirectToProvider(Request $request, $provider)
    {
        try {
            Session::put('queryBeforeLogin', request()->query());

            if ($request->has('redirect_uri')) {
                session()->put('redirect_uri', $request->get('redirect_uri'));
            }

            return Socialite::driver($provider)->redirect();
        } catch (Exception $ex) {
            Log::warning('LoginController@redirectToProvider: ' . $ex->getMessage());

            return redirect()->route('login');
        }
    }

    public function oneTap($provider, Request $request, BaseHttpResponse $response)
    {
        if ($request->has('redirect_uri')) {
            session()->put('redirect_uri', $request->get('redirect_uri'));
        }

        if ($request->has('referring_name')) {
            session()->put('referring_name', $request->get('referring_name'));
        }

        try {
            $access_token = $request->credential ?? $request->data['credential'];
            $oAuth = Socialite::driver($provider)->userFromToken($access_token);
            $display_name = $oAuth->getName();

            $user = User::where('email', $oAuth->getEmail())->first();

            if (!$user || $user->authentications()->count() == 0) {
                return view('auth.verify-user', ['auth' => Crypt::encryptString(json_encode($oAuth)), 'display_name' => $display_name, 'provider' => $provider]);
            }

            if ($user->isFreezing()) {
                return $response
                    ->setError()
                    ->setNextUrl(route('login') . '#nav-developer')
                    ->setMessage(trans('validation.error-freeze'));
            }

            Auth::login($user);

            return $this->sendLoginResponse($request);
        } catch (\Exception $ex) {
            Log::error("LoginController@oneTap:" . $ex->getMessage(), ['request' => $request->all(), $request->cookie()]);

            app('sentry')->captureException($ex);

            return $response
                ->setError()
                ->setNextUrl(route('login') . '#nav-employer')
                ->setMessage($ex->getMessage());
        }
    }


    /**
     * Handle the provider's callback after the user has authenticated with the provider.
     *
     * This method is responsible for handling the callback from the OAuth provider after the user has authenticated.
     * It retrieves the user's information from the provider and checks if the user already exists in the application's database.
     * If the user does not exist, it redirects the user to a view where they can verify their information.
     * If the user does exist, it logs the user in and redirects them to the application's home page.
     *
     * @param Request $request
     * @param BaseHttpResponse $response
     * @param string|null $provider
     *
     * @return Application|Factory|Response|View|\Redmix0901\Core\Http\Responses\BaseHttpResponse
     */
    public function handleProviderCallback(Request $request, BaseHttpResponse $response, string $provider = null)
    {
        if ($request->input('error') == 'access_denied') {
            return $response
                ->setError()
                ->setNextUrl(route('login'));
        }

        try {
            $oAuth = Socialite::driver($provider)->user();
            $display_name = $provider == 'apple' ? $oAuth->getEmail() : $oAuth->getName();
            $userEmail = $oAuth->getEmail();
            $user = User::query()
                ->where('email', $userEmail)->first();

            if (!$user || $user->authentications()->count() == 0) {
                Log::info('LoginController@handleProviderCallback: user verification process ' . $userEmail);

                return view('auth.verify-user', [
                    'auth' => Crypt::encryptString(json_encode($oAuth)),
                    'display_name' => $display_name,
                    'provider' => $provider,
                ]);
            }

            // $user->setMeta($provider, $this->getResourceOwner($provider, $oAuth));

            if ($user->isFreezing()) {
                return $response
                    ->setError()
                    ->setNextUrl(route('login') . '#nav-developer')
                    ->setMessage(trans('validation.error-freeze'));
            }

            Auth::login($user);

            return $this->sendLoginResponse($request);
        } catch (\Exception $ex) {
            Log::error("LoginController@handleProviderCallback:" . $ex->getMessage(), ['cookie' => $request->cookie()]);

            app('sentry')->captureException($ex);

            return $response
                ->setError()
                ->setNextUrl(route('login') . '#tab-employer')
                ->setMessage($ex->getMessage());
        }
    }

    public function verifyUser(Request $request, BaseHttpResponse $response)
    {
        try {
            $auth = json_decode(Crypt::decryptString($request->auth));
            $display_name = $request->display_name;
            $provider = $request->provider;
            $email = $auth->email;
            $register = false;
            /**
             * @var User $user
             */
            $user = User::withTrashed()
                ->where('email', $email)
                ->first();

            if (!$user instanceof User) {
                $register = true;
                // Create a new user
                $user = DB::transaction(function () use ($email, $display_name) {
                    $newUser = User::create([
                        'email' => $email,
                        'display_name' => $display_name,
                        'type' => User::RESUME_TYPE,
                        'email_verified_at' => now(),
                    ]);

                    $newUserProfile = $this->createUserProfileByUserId($newUser->id);

                    $this->createUserMainCv($newUser->id, $newUserProfile->id);

                    // Insert search candidate
                    $this->createSearchCandidateByUserId($newUser->id);

                    return $newUser;
                });
            }

            $this->restoreIfUserHasDeleted($user);

            $this->addTrackingUtm($request, $user);

            if ($user->isFreezing()) {
                return $response
                    ->setError()
                    ->setNextUrl(route('login') . '#nav-developer')
                    ->setMessage(trans('validation.error-freeze'));
            }

            Auth::login($user);

            return $this->sendLoginResponse($request, true, $register);
        } catch (\Exception $ex) {
            Log::error("LoginController@verifyUser:" . $ex->getMessage(), ['cookie' => $request->cookie()]);

            app('sentry')->captureException($ex);

            return $response
                ->setError()
                ->setNextUrl(route('login') . '#tab-employer')
                ->setMessage($ex->getMessage());
        }
    }

    /**
     * @param int $userId
     *
     * @return mixed
     */
    private function createUserProfileByUserId(int $userId)
    {
        $userProfile = UserProfile::where('user_id', $userId)->first();

        if ($userProfile instanceof UserProfile) {
            return $userProfile;
        }

        try {
            return UserProfile::create([
                'user_id' => $userId,
                'skills' => (object)[],
                'experiences' => [],
                'educations' => [],
                'projects' => [],
                'languages' => [],
                'interests' => [],
                'references' => [],
                'activities' => [],
                'certificates' => [],
                'additionals' => [],
                'completed_sections' => [],
            ]);
        } catch (\Exception $ex) {
            Log::error("LoginController@createUserProfileByUserId:" . $ex->getMessage());

            return false;
        }
    }

    private function createUserMainCv(int $userId, int $userProfileId)
    {
        $userMainCv = UserMainCv::where('user_id', $userId)
            ->where('cv_id', $userProfileId)
            ->where('cv_type', UserProfile::class)
            ->first();

        if ($userMainCv instanceof UserMainCv) {
            return $userMainCv;
        }

        try {
            return UserMainCv::create([
                'user_id' => $userId,
                'cv_id' => $userProfileId,
                'cv_type' => UserProfile::class,
            ]);
        } catch (\Exception $ex) {
            Log::error("LoginController@createUserMainCv:" . $ex->getMessage());

            return false;
        }
    }

    private function createSearchCandidateByUserId(int $userId)
    {
        try {
            return DB::connection('applancer_ms')
                ->table('search_candidates')
                ->insert([
                    'user_id' => $userId,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);
        } catch (\Exception $ex) {
            Log::error("LoginController@createSearchCandidateByUserId:" . $ex->getMessage());

            return false;
        }

    }

    /**
     * Check login referer to put session and use it later
     *
     * @param \Illuminate\Http\Request $request
     * @return void
     */
    private function checkLoginRererer(Request $request)
    {
        session()->forget('login_referrer_url');

        // If having referrer and come from our site
        // Then set session to take exclude event from that page
        $referer = $request->header('referer');
        if ($referer) {
            $appDomain = parse_url(config('app.url'), PHP_URL_HOST);
            // just get domain, not subdomain. e.g test.example.com => example.com
            $appDomain = implode('.', array_slice(explode('.', $appDomain), -2));
            $refererDomain = parse_url($referer, PHP_URL_HOST);
            $refererDomain = implode('.', array_slice(explode('.', $refererDomain), -2));
            // If it's referered from our side, need put session to exclude event from that page
            if ($appDomain === $refererDomain) {
                session()->put('login_referer_url', $refererDomain);
            }
        }
    }

    /**
     * Tracking utm when new user is signed up to our system
     *
     * @param \Illuminate\Http\Request $request
     * @param \Modules\User\Entities\User $user
     * @return void
     */
    private function addTrackingUtm(Request $request, User $user)
    {
        try {
            $sessionIds = Str::getSessionId($request->cookie('ta'));
            // If exists
            if (count($sessionIds) == 2) {
                $sessionId = $sessionIds[0];
                $sessionNumber = $sessionIds[1];
                $trackingEvent = UserCollectEvent::query()
                    ->whereSessionId($sessionId)
                    ->whereHas('userCollect', fn($query) => $query->whereSessionId($sessionId)->whereSessionNumber($sessionNumber))
                    ->with([
                        'userCollect' => fn($query) => $query->whereSessionId($sessionId)->whereSessionNumber($sessionNumber)
                    ])
                    ->when(session()->get('login_referer_url'), fn($query) => $query->whereNotIn('event_name', ['btnLoginGoogle', 'btnLoginGithub']))
                    ->latest('id')
                    ->first();

                if ($trackingEvent) {
                    $user->fill([
                        'utm_source' => $trackingEvent->userCollect->utm_source,
                        'utm_medium' => $trackingEvent->userCollect->utm_medium,
                        'utm_campaign' => $trackingEvent->userCollect->utm_campaign,
                        'utm_content' => $trackingEvent->userCollect->utm_content,
                        'utm_term' => $trackingEvent->userCollect->utm_term,
                        'event_group' => $trackingEvent->event_group,
                        'event_name' => $trackingEvent->event_name,
                        'tracking_page' => $trackingEvent->event_page,
                        'tracking_location' => $trackingEvent->event_document_link,
                    ])->save();
                }
                session()->forget('login_referrer_url');
            }
        } catch (Exception $ex) {
            Log::error("LoginController@addTrackingUtm:" . $ex->getMessage(), ['ta-cookie' => $request->cookie('ta')]);
            app('sentry')->captureException($ex);
        }
    }

    protected function redirectJson($request)
    {
        $request->session()->regenerate();
        $this->clearLoginAttempts($request);
        $session_id = session()->getId();
        $config = $this->config->get('session');

        return response()->json(true)
            ->cookie($config['cookie'], $session_id, $config['lifetime'], $config['path'], $config['domain']);
    }

    protected function getResourceOwner($provider, $oAuth)
    {
        try {
            if ($provider == 'twitter') {
                $resource = Socialite::driver($provider)->userFromTokenAndSecret($oAuth->token, $oAuth->tokenSecret);
            } else {
                $resource = Socialite::driver($provider)->userFromToken($oAuth->token);
            }

            return $resource->user;
        } catch (Exception $e) {
            return null;
        }
    }

    protected function sendFailedLoginResponse(Request $request)
    {
        $user = User::onlyTrashed()
            ->where(function ($query) use ($request) {
                $query->where('username', $request->email)
                    ->orWhere('email', $request->email);
            })
            ->first();

        if ($user && Hash::check($request->password, $user->password)) {
            $user->restore();
            Auth::login($user);
            return $this->sendLoginResponse($request);
        }

        if ($this->checkUserWithUsername($request)) {

            // If user input username.
            // Try login username & password hash.
            if ($this->attemptLoginWithHash($request, 'username')) {
                return $this->sendLoginResponse($request);
            }

            // User input password wrongs.
            throw ValidationException::withMessages([
                'password' => [trans('auth.emailExists')],
            ])->redirectTo('/#tab-employer');
        }

        throw ValidationException::withMessages([
            'email' => [trans('auth.usernameNotExists')],
        ])->redirectTo('/#tab-employer');
    }

    /**
     * Attempt to log the user into the application with password ecrypt by hash.
     *
     * @param Request $request
     *
     * @return bool
     */
    protected function attemptLoginWithHash($request, $username = 'email')
    {
        return $this->guard()->attempt(
            [
                $username => $request->email,
                'password' => $request->password
            ],
            $request->filled('remember')
        );
    }

    /**
     * Check user exits with username.
     *
     * @param Request $request
     *
     * @return bool
     */
    protected function checkUserWithUsername($request)
    {
        return $this->userRepository->getFirstBy(['username' => $request->email]);
    }

    /**
     * Get the post register / login redirect path.
     *
     * @return string
     */
    public function redirectTo(): string
    {
        $url = auth()->user()->role('employer') ?
            (config('app.dash_topdev_url') ?? '/') : (config('app.dashdev_topdev_url') ?? '/');

        $params = Session::pull('queryBeforeLogin', []);
        if (count($params)) {
            $url .= '?' . http_build_query($params);
        }

        return $url;
    }

    /**
     * {@inheritdoc}
     */
    protected function sendLoginResponse(Request $request, $signup = false, $register = false)
    {
        if ((!$request->user()->hasVerifiedEmail() && !$request->user()->hasApprovedAccount()) && $request->user()->role('employer')) {
            Auth::logout();

            return redirect('/')->with('alert', 'Verify your email!')
                ->with('text', 'Please check your email for verification.')
                ->with('footer', "If you don't receive email, please click here");
        }
        if ($request->user()->isFreezing()) {
            Auth::logout();

            throw ValidationException::withMessages([
                'freeze' => [trans('validation.error-freeze')],
            ])->redirectTo('/#tab-employer');
        }

        // $request->session()->regenerate();

        $this->clearLoginAttempts($request);

        $user = $this->guard()->user();
        if ($request->user()->role('employer')) {
            $request->user()->sendEmployerLoginNotification();
        }
        $config = $this->config->get('session');
        $expiration = Carbon::now()->addMinutes($config['lifetime']);

        $cookie = new Cookie(
            $this->cookieName,
            $user->uuid,
            $expiration,
            $config['path'],
            $config['domain'],
            $config['secure'],
            false,
            false,
            $config['same_site'] ?? null
        );

        return $this->trackingView($request, $user, $cookie, $signup, $register);
    }

    protected function authenticated(Request $request, $user)
    {
        // Enable User-ID Feature (Expires Session)
        $userIdCookie = Cookie::create('ATDUID', $user->getAuthIdentifier(), 0, '/', config('session.domain'));
        \Illuminate\Support\Facades\Cookie::queue($userIdCookie);

        return false;
    }


    protected function loggedOut(Request $request)
    {
        $domain = config('session.domain');

        \Illuminate\Support\Facades\Cookie::queue(\Illuminate\Support\Facades\Cookie::forget('ATDUID', '/', $domain));
        \Illuminate\Support\Facades\Cookie::queue(\Illuminate\Support\Facades\Cookie::forget($this->cookieName, '/', $domain));
        \Illuminate\Support\Facades\Cookie::queue(\Illuminate\Support\Facades\Cookie::forget('topdev_token', '/', $domain));
        \Illuminate\Support\Facades\Cookie::queue(\Illuminate\Support\Facades\Cookie::forget('referring_name', '/', $domain));
        \Illuminate\Support\Facades\Cookie::queue(\Illuminate\Support\Facades\Cookie::forget('just_signup', '/', $domain));

        foreach (\Illuminate\Support\Facades\Cookie::get() as $key => $value) {
            if (strpos($key, 'remember_web_') !== false) {
                \Illuminate\Support\Facades\Cookie::queue(
                    \Illuminate\Support\Facades\Cookie::forget($key, '/', $domain)
                );
            }
        }

        return redirect()->to('/');
    }

    public function cleanUp(Request $request)
    {
        $this->loggedOut($request);

        return redirect()->to('/');
    }

    protected function restoreIfUserHasDeleted(User $user)
    {
        if ($user->trashed()) {
            $user->restore();
            $user->wasRecentlyCreated = true;
        }

        $userProfile = $this->createUserProfileByUserId($user->id);

        $this->createUserMainCv($user->id, $userProfile->id);

        if (!DB::connection('applancer_ms')
            ->table('search_candidates')
            ->where('user_id', $user->id)
            ->exists()
        ) {
            $this->createSearchCandidateByUserId($user->id);
        }
    }

    public function trackingView($request, User $user, $cookie, $signup = false, $register = false)
    {
        $this->authenticated($request, $user);

        $redirectTo = session()->get('redirect_uri', $this->redirectPath());
        \Illuminate\Support\Facades\Cookie::queue($cookie);

        // Tracking view
        $hash = Hash::make($user->uuid);
        Cache::put('trackingView-' . $user->id, $hash, 600);

        // GTM tracking
        GoogleTagManager::push('user_id', $user->id);
        GoogleTagManager::push('event', 'login');
        GoogleTagManager::push('referring_name', session()->get('referring_name'));
        // Tracking from remind sign up
        if (($referringName = session('referring_from')) && $referringName === 'remind_sign_up') {
            GoogleTagManager::push('event', $register ? 'user_signup_from_remind_popup' : 'user_login_from_remind_popup');
        }
        if ($signup) {
            GoogleTagManager::push('event', 'sign_up');

            Log::info('User sign up', [
                'user_id' => $user->id,
                'email' => $user->email,
                'created_at' => $user->created_at,
                'first_login_at' => optional($user->authentications->first())->login_at,
                'cookies' => $request->cookie(),
            ]);

            cookie()->queue('just_signup', 1, 0, null, null, null, false);
        }

        return view('tracking', compact('redirectTo', 'user', 'hash'));
    }
}
