<?php

namespace App\Http\Controllers\Auth;

use Auth;
use App\Http\Controllers\Controller;
use Illuminate\Foundation\Auth\ResetsPasswords;
use Illuminate\Http\Request;
use Modules\User\Repositories\Contracts\UserRepositoryInterface;
use <PERSON><PERSON>\Passport\HasApiTokens;
use Modules\User\Entities\User;
use Modules\User\Notifications\SendResetPassword;
use Illuminate\Support\Facades\Validator;
use Carbon\Carbon;
use Str;

class ResetPasswordController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Password Reset Controller
    |--------------------------------------------------------------------------
    |
    | This controller is responsible for handling password reset requests
    | and uses a simple trait to include this behavior. You're free to
    | explore this trait and override any methods you wish to tweak.
    |
    */

    use ResetsPasswords;
    use HasApiTokens;

    /**
     * Where to redirect users after resetting their password.
     *
     * @var string
     */
    protected $redirectTo = '/home';

    protected $userRepository;

    private const USER_EMAIL_KEY = 'user.email';

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct(UserRepositoryInterface $user)
    {
        $this->middleware('guest');
        $this->userRepository = $user;
    }

    public function sendMail(Request $request)
    {
        $user = User::where('email', $request->email)->first();

        if ($user) {
            // If users type is not employer then returns errors
            if ($user->type !== User::EMPLOYER_TYPE) {
                return response()->json([
                    'error' => trans('home.not-employer-email')
                ]);
            }
            $user->resetPasswordUser();
            return response()->json([
                'success' => 'success',
            ]);
        }

        return response()->json([
            'error' => trans('home.email-not-found')
        ]);
    }

    protected function validator(array $data)
    {
        return Validator::make($data, [
            'password'      => ['required', 'string', 'min:8'],
            'password_comfirm' => ['required', 'same:password'],
            'email' => ['required', 'email']
        ]);
    }

    protected function validatorEmail(array $data)
    {
        return Validator::make($data, [
            'email'      => ['required', 'email']
        ]);
    }

    public function showFormChangePassword(Request $request)
    {
        // Should cleanup eveytime when access this link to avoid setting wrong email
        session()->forget(self::USER_EMAIL_KEY);
        $expires = $request->query('expires');
        if (($expires && Carbon::now()->getTimestamp() > $expires)) {
            return redirect()->route('broken-link');
        }

        try {
            [$id, $email] = explode('|', decrypt($request->route('hash')));
            $user = User::find($id);

            if (!$user || $email !== $user->email) {
                throw new \Exception('User invalid');
            }

            // Store valid email to session then use it in the reset method
            session()->put(self::USER_EMAIL_KEY, $user->email);
        } catch (\Throwable $throwable) {
            return redirect('https://topdev.vn/page-not-found');
        }

        return view('auth.passwords.reset', compact('email'));
    }

    public function reset(Request $request)
    {
        $formData = array_merge($request->all(), ['email' => session()->get(self::USER_EMAIL_KEY)]);
        $validator = $this->validator($formData);

        if ($validator->passes()) {
            $user = User::where('email', $formData['email'])->first();

            $user->update([
                'password' => $request->password_comfirm
            ]);

            // Should cleanup after done reset process to avoid get wrong email
            session()->forget(self::USER_EMAIL_KEY);
            return redirect('/')->with('success', trans('home.update-success'))->with('text', trans('home.change-success'));
        }

        return redirect()->back()->withErrors($validator);
    }

    public function getReset()
    {
        return view('auth.passwords.email');
    }

    public function changePassword(Request $request)
    {
        if (Auth::guard()->attempt([
            'email' => $request->email,
            'password' => $request->password
        ])) {
            $user = Auth::guard()->user();

            $this->revokeAndRefresh($user);
            $this->updatePassword($user, $request->password_comfirm);
            $this->createToken($user);
        } else {
            $user = $this->userRepository->getfirstBy([
                'email' => $request->email,
                'password' => md5($request->password)
            ]);

            if ($user) {
                $this->revokeAndRefresh($user);
                $this->updatePassword($user, $request->password_comfirm);
                $this->createToken($user);
            } else {
                dd("khong ton tai user");
            }
        }
    }

    public function revokeAndRefresh($user)
    {
        foreach ($user->tokens as $key => $token) {
            $token->revoke();
            if ($token->refreshToken) {
                $token->refreshToken->revoke();
            }
        }
    }

    public function updatePassword($user, $passwordUpdate)
    {
        $user->password = bcrypt($passwordUpdate);
        $user->save();
    }

    public function createToken($user)
    {
        $user->createToken('access_token')->accessToken;
    }
}
