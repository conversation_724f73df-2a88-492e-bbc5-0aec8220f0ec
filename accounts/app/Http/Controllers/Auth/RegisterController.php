<?php

namespace App\Http\Controllers\Auth;

use App\Facades\Unleash;
use App\Helpers\FeatureFlag;
use App\Http\Controllers\Controller;
use App\Services\RecaptchaService;
use Carbon\Carbon;
use GuzzleHttp\Client;
use Illuminate\Foundation\Auth\RegistersUsers;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cookie;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Modules\User\Entities\User;
use Modules\User\Repositories\Contracts\UserRepositoryInterface;
use Redmix0901\Core\Http\Responses\BaseHttpResponse;

class RegisterController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Register Controller
    |--------------------------------------------------------------------------
    |
    | This controller handles the registration of new users as well as their
    | validation and creation. By default this controller uses a trait to
    | provide this functionality without requiring any additional code.
    |
    */

    use RegistersUsers;

    /**
     * Where to redirect users after registration.
     *
     * @var string
     */
    protected $redirectTo = 'home';

    /**
     * @var UserRepositoryInterface
     */
    protected $userRepository;

    protected $token;

    protected $client;

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct(UserRepositoryInterface $user, Client $client)
    {
        $this->middleware('guest');
        $this->userRepository = $user;
        $this->client = $client;
    }

    /**
     * Get a validator for an incoming registration request.
     *
     * @return  \Illuminate\Validation\Validator
     */
    protected function validator(array $data): \Illuminate\Validation\Validator
    {
        if (Unleash::isEnabled(FeatureFlag::DEV_2062_FREE_POST)) {
            return $this->getValidatorForDev2062($data);
        }

        return $this->getValidator($data);
    }

    private function getValidator($data)
    {
        $roles = [
            'display_name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:applancer_ms.users'],
            'password' => ['required', 'string', 'min:8'],
            'password_confirmation' => ['required', 'same:password'],
            'phone' => ['required', 'regex:/(84|0[3|5|7|8|9])+([0-9]{8})\b/'],
            'company_id' => 'nullable',
            'skills' => ['required', 'string', 'max:255'],
            'industry' => ['required', 'string', 'max:255'],
        ];

        $messages = [
            'display_name.required' => trans('home.error_display_name_required'),
            'display_name.max' => trans('home.error_display_name_max'),
            'email.required' => trans('home.error_email_required'),
            'email.email' => trans('home.error_email_email'),
            'email.unique' => trans('home.error_email_unique'),
            'password.required' => trans('home.error_password_required'),
            'password.min' => trans('home.error_password_min'),
            'password_confirmation.required' => trans('home.error_password_confirm_required'),
            'password_confirmation.same' => trans('home.error_password_confirm_same'),
            'phone.required' => trans('home.error_phone_required'),
            'phone.regex' => trans('home.error_phone_phone'),
            'skills.required' => trans('home.error_skills_required'),
            'industry.required' => trans('home.error_industry_required'),
        ];

        $roles = array_merge($roles, [
            'location' => ['required'],
            'business_name' => ['required', 'string', 'max:255'],
            'tax_number' => ['required', 'string', 'regex:/^(\d{10}|\d{10}-\d{3})$/'],
        ]);

        $messages = array_merge($messages, [
            'location.required' => trans('home.error_province_required'),
            'business_name.required' => trans('home.error_business_name_required'),
            'tax_number.required' => trans('home.error_tax_number_required'),
            'tax_number.regex' => trans('home.error_tax_number_tax_number'),
        ]);

        return Validator::make($data, $roles, $messages);
    }

    /**
     * Get a validator for an incoming registration request.
     * Feature flag for DEV-2062
     *
     * @return  \Illuminate\Validation\Validator
     */
    protected function getValidatorForDev2062(array $data): \Illuminate\Validation\Validator
    {
        $rules = [
            'employer_display_name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:applancer_ms.users'],
            'password' => ['required', 'string', 'min:8'],
            'password_confirmation' => ['required', 'same:password'],
            'phone' => ['required', 'regex:/(84|0[3|5|7|8|9])+([0-9]{8})\b/'],
            'company_id' => 'nullable',
            'business_name' => ['required', 'string', 'max:255'],
            'tax_number' => ['required', 'string', 'regex:/^(\d{10}|\d{10}-\d{3})$/'],
        ];

        $messages = [
            'employer_display_name.required' => trans('home.error_employer_display_name_required'),
            'employer_display_name.max' => trans('home.error_employer_display_name_max'),
            'email.required' => trans('home.error_email_required'),
            'email.email' => trans('home.error_email_email'),
            'email.unique' => trans('home.error_email_unique'),
            'password.required' => trans('home.error_password_required'),
            'password.min' => trans('home.error_password_min'),
            'password_confirmation.required' => trans('home.error_password_confirm_required'),
            'password_confirmation.same' => trans('home.error_password_confirm_same'),
            'phone.required' => trans('home.error_phone_required'),
            'phone.regex' => trans('home.error_phone_phone'),
            'business_name.required' => trans('home.error_business_name_required'),
            'tax_number.required' => trans('home.error_tax_number_required'),
            'tax_number.regex' => trans('home.error_tax_number_tax_number'),
        ];

        return Validator::make($data, $rules, $messages);
    }

    /**
     * Create a new user instance after a valid registration.
     *
     * @return \Modules\User\Entities\User
     */
    protected function create(array $data)
    {
        return $this->userRepository
            ->create([
                'display_name' => $data['display_name'],
                'email' => $data['email'],
                'username' => $data['username'],
                'password' => Hash::make($data['password']),
            ]);
    }

    protected function verifyRecaptcha(Request $request): bool
    {
        if (!config('services.google_recaptcha.enable')) {
            return true;
        }

        $token = $request->input('token');

        $recaptchaService = app(RecaptchaService::class);
        return $recaptchaService->verify($request, $token);
    }

    public function register(Request $request)
    {
        if (!$this->verifyRecaptcha($request)) {
            return response()->json(
                ['error' => ['token' => "reCAPTCHA verification failed. Please try again."]]
            );
        }

        $validator = $this->validator($request->all());
        if ($validator->passes()) {
            $responseUser = $this->postToCreateUser($request, rtrim(config('app.api_url'), '/').'/td/v2/register');

            if ($responseUser->getStatusCode() == 200) {
                $res = json_decode($responseUser->getBody()->__toString(), true);
                if (! empty($res['error']) && isset($res['data']) && $res['data'] == 'ERR_PAID_ALREADY') {
                    return response()->json(
                        ['error' => ['tax_number' => [trans('home.error_tax_number_aready_existed')]]]
                    );
                }

                return response()->json(
                    [
                        'html' => view('auth.confirm-register', ['email' => $request->email])->render(),
                    ]
                );
            }
        }

        return response()->json(
            [
                'error' => $validator->errors(),
            ]
        );
    }

    private function getCreateUserRequestBodyOriginal($request): array
    {
        return [
            'display_name' => $request->display_name,
            'email' => $request->email,
            'phone' => $request->phone,
            'type' => 'employer',
            'password' => $request->password,
            'seeking_candidate' => ($request->seeking_candidate != 0) ? true : null,
            'company' => [
                'display_name' => $request->display_name,
                'tax_number' => $request->tax_number,
                'business_name' => $request->business_name,
                'addresses' => [
                    [
                        'province_id' => $request->location,
                    ],
                ],
                'industries' => array_slice(explode(',', $request->industry), 0),
                'skills' => array_slice(explode(',', $request->skills), 0),
            ],
        ];
    }

    private function getCreateUserRequestBodyForDev2062($request)
    {
        return [
            'employer_display_name' => $request->employer_display_name,
            'email' => $request->email,
            'phone' => $request->phone,
            'type' => 'employer',
            'password' => $request->password,
            'seeking_candidate' => ($request->seeking_candidate != 0) ? true : null,
            'company' => [
                'tax_number' => $request->tax_number,
                'business_name' => $request->business_name,
            ],
        ];
    }

    private function getCreateUserRequestBody($request)
    {
        if (Unleash::isEnabled(FeatureFlag::DEV_2062_FREE_POST)) {
            return $this->getCreateUserRequestBodyForDev2062($request);
        }

        return $this->getCreateUserRequestBodyOriginal($request);
    }

    public function postToCreateUser($request, $link)
    {
        return $this->client->post($link, [
            'headers' => [
                'Accept' => 'application/json',
            ],
            'body' => json_encode($this->getCreateUserRequestBody($request)),
        ]);
    }

    public function verify(Request $request)
    {
        $expires = $request->query('expires');

        if (($expires && Carbon::now()->getTimestamp() > $expires)) {
            return redirect()->route('broken-link');
        }

        $user = User::find($request->id);

        if (! is_null($user->email_verified_at)) {
            return redirect('/')->with('warning', trans('home.verified'))->with('text', trans('home.text-verified'));
        }

        if (hash_equals((string) $request->hash, sha1($user->email))) {
            $user->markEmailAsVerified();

            return redirect('/')->with('success', trans('home.successfully'))->with('text', trans('home.verify-success'));
        }

        return redirect('/')->with('alert', 'Oop!')->with('text', 'Something goes wrong!');
    }

    public function resendMail(Request $request)
    {
        $user = User::where('email', $request->email)->first();

        if ($user) {
            if (! is_null($user->email_verified_at)) {
                return response()->json([
                    'icon' => 'warning',
                    'title' => trans('home.verified'),
                    'text' => trans('home.text-verified'),
                ]);
            }
            //  Gui resend
            $user->verifyEmailRegister();

            return response()->json([
                'icon' => 'success',
                'title' => trans('home.successfully'),
                'text' => trans('home.please-check-your-email'),
            ]);
        }

        return response()->json([
            'icon' => 'error',
            'title' => trans('home.not-found'),
            'text' => trans('home.email-not-found'),
        ]);
    }

    // public function confirm(Request $request){
    //     $email = $request->email;
    //     return view('auth.confirm-register',compact('email'));
    // }

    public function setcookie($request, $token)
    {
        Cookie::queue(Cookie::make('td_account', $token, 2));
    }

    public function createToken($user)
    {
        return $user->createToken('userToken')->accessToken;
    }
}
