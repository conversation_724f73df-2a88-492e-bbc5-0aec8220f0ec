<?php

namespace App\Http\Controllers\Auth;

use MagicLink\MagicLink;
use Illuminate\Http\Request;
use App\Jobs\DeleteResumeData;
use Modules\User\Entities\User;
use MagicLink\Actions\LoginAction;
use App\Http\Controllers\Controller;
use App\Http\Requests\DeleteRequest;
use Illuminate\Support\Facades\Auth;
use Modules\User\Entities\QuestionRemove;
use Modules\User\Entities\QuestionAnswerRemove;
use Modules\VerificationCode\Facade\VerificationCode;

class DeleteController extends Controller
{
    public function magicLink()
    {
        $lifetime = 10; // 10 minutes
        $action = new LoginAction(Auth::user());
        $action->guard('web')->response(redirect('/remove/request'));
        $urlShowView = MagicLink::create($action, $lifetime)->url;

        return response()->json([
            'message' => 'success',
            'redirect_url' => $urlShowView
        ]);
    }

    public function getQuestion()
    {
        $user = Auth::user();
        $question = QuestionRemove::all();

        return view('auth.delete-account')
            ->with([
                'question' => $question,
                'user' => $user
            ]);
    }

    public function requestDelete(DeleteRequest $request)
    {
        $user = Auth::user();
        if ($user) {
            QuestionAnswerRemove::updateOrCreate(
                [
                    'user_id' => $user->id,
                ],
                [
                    'question_id' => $request->question_id,
                    'reason' => $request->reason
                ]
            );

            return response()->json([
                'message' => 'success'
            ], 200);
        }
        return response()->json([
            'message' => 'error'
        ], 400);
    }

    public function deleteAccount(Request $request)
    {
        $user = $request->user();
        if (VerificationCode::verify($request->code, $user->email)) {
            $user->markAccountAsFreezing();
            DeleteResumeData::dispatch($user->getKey());
            Auth::logout();

            return response([
                'message' => 'success'
            ], 200);
        }

        return response([
            'message' => 'Incorrect code',
        ]);
    }

    public function sendConfirmEmail()
    {
        $user = Auth::user();
        if ($user) {
            VerificationCode::send($user->email);
        }
    }
}
