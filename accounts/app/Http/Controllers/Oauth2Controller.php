<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Auth\Events\Logout;
use Illuminate\Support\Facades\Log;
use App\Jobs\CreateFcmDeviceTokenActivity;
use Redmix0901\Core\Http\Responses\BaseHttpResponse;

class Oauth2Controller extends Controller
{
    public function revokeToken(Request $request, BaseHttpResponse $response)
    {
        $user = $request->user();
        $source = $request->header('X-Topdev-Source') ?? '';
        $fcmToken = $request->header('X-Topdev-Device-Token') ?? '';

        event(new Logout('api', $user));

        if ($source == 'MobileApp' && !empty($fcmToken)) {
            Log::info('User [' . $user->email . '] REVOKE the access token on logout from MobileApp with device token [' . $fcmToken. ']');
            CreateFcmDeviceTokenActivity::dispatch($user->getKey(), [
                'action' => 'logout',
                'fcm_device_token' => $fcmToken,
                'source' => 'MobileApp'
            ]);
        }

        return $response->setError(false)
                ->setMessage([
                    'message' => 'logout',
                ]);
    }
}
