<?php

namespace App\Http\Middleware;

use Closure;
use Modules\User\Entities\User;

class PermissionUser
{
    /**
     * Enforce json
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @param  string|null  $guard
     * @return mixed
     */
    public function handle($request, Closure $next, $ability = null)
    {
        if (!empty($request->user()) && $request->user() instanceof User && $request->user()->hasPermission($ability)) {
            return $next($request);
        }

        return response()->json([
            'message' => "you doesn't have permission",
            'success' => false,
        ]);
    }
}
