<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Support\Facades\Auth;

class TopDevEngineer
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        if (auth()->check() && $this->isEngineer(auth()->user())) {
            return $next($request);
        }

        abort(403);
    }

    protected function engineers(): array
    {
        return [
            '<EMAIL>'
        ];
    }

    private function isEngineer($user)
    {
        return in_array($user->email, $this->engineers());
    }
}
