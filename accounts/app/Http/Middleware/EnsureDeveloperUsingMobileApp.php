<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Support\Facades\Auth;
use Illuminate\Container\Container;
use League\OAuth2\Server\Exception\OAuthServerException;
use Symfony\Bridge\PsrHttpMessage\Factory\DiactorosFactory;
use App\Entities\Client;
use Lcobucci\JWT\Parser;
use Modules\User\Entities\User;

class EnsureDeveloperUsingMobileApp
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        $response = $next($request);

        return $response;

        if (!in_array($request->path(), $this->checkFor()) || empty($request->client_id) || $request->client_id != env('CLIENT_MOBILE', 0)) {
            return $response;
        }

        $token = json_decode($response->getContent(), true)['access_token'] ?? null;

        if (!empty($token)) {
            $user = app(Parser::class)->parse($token)->getClaims()['sub']->getValue();
            $user = User::find($user);
        }

        if (isset($user) && !$user->role('resume')) {
            return response()->json([
                'code' => 403,
                'message' => 'This action is unauthorized.',
            ], 403);
        }

        return $response;
    }

    protected function checkFor()
    {
        return [
            'oauth/token'
        ];
    }
}
