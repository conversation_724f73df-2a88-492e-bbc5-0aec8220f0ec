<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class RedirectIfAuthenticated
{
    /**
     * Handle an incoming request.
     *
     * @param  Request  $request
     * @param  \Closure  $next
     * @param  string|null  $guard
     * @return mixed
     */
    public function handle(Request $request, Closure $next, $guard = null)
    {
        $auth = Auth::guard($guard);

        if ($auth->check()) {
            return redirect()->to($this->getRedirectTo($request, $auth));
        }

        return $next($request);
    }

    protected function getRedirectTo(Request $request, $auth)
    {
        $url = $auth->user()->role('employer')
            ? (env('DASH_TOPDEV_URL') ?? '/')
            : (env('DASHDEV_TOPDEV_URL') ?? '/');

        if (count($request->query())) {
            $url .= '?' . http_build_query($request->query());
        }

        return $url;
    }
}
