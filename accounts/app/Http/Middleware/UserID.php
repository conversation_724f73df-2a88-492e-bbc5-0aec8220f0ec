<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Support\Facades\Auth;
use <PERSON><PERSON>\GoogleTagManager\GoogleTagManager;

class UserID
{
    /**
     * @var GoogleTagManager
     */
    protected $googleTagManager;

    public function __construct(GoogleTagManager $googleTagManager)
    {
        $this->googleTagManager = $googleTagManager;
    }

    /**
     * Handle an incoming request.
     *
     * @param \Illuminate\Http\Request $request
     * @param Closure $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        if (Auth::check() && $request->user()->id) {
            $data = ['user_id' => $request->user()->id]; // Auth::id() raise Error Call to undefined method App\User::getAuthIdentifier()
            $this->googleTagManager->set($data);
            // logger(__CLASS__.' auth check', $data);
        }

        // https://github.com/protonemedia/laravel-analytics-event-tracking

        // logger(__METHOD__, $this->googleTagManager->getDataLayer()->toArray());

        return $next($request);
    }
}
