<?php

namespace App\Http\Requests;

use Illuminate\Support\Facades\Session;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Str;

class ShowFormLoginRequest extends FormRequest
{
    const FLAG_LOGIN_TO_VIEW_STATUS_APPLY = 'LOGIN_TO_VIEW_STATUS_APPLY';
    const FLAG_TOPDEV_BLOG = 'TOPDEV_BLOG';
    const FLAG_LOGIN_TO_VIEW_EMPLOYER_DASH_CANDIDATE = 'EMPLOYER_DASH_VIEW_CANDIDATE';

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [];
    }

    public function hasFlag($flag)
    {
        $queryParam = parse_url(Session::previousUrl(), PHP_URL_QUERY);

        if (is_null($queryParam)) {
            return false;
        }

        return Str::contains($queryParam, $flag);
    }

    public function isFromViewStatusApply()
    {
        return $this->hasFlag(static::FLAG_LOGIN_TO_VIEW_STATUS_APPLY);
    }

    public function isFromBlog()
    {
        return $this->hasFlag(static::FLAG_TOPDEV_BLOG);
    }

    public function isFromViewDashCandidate()
    {
        return $this->hasFlag(static::FLAG_LOGIN_TO_VIEW_EMPLOYER_DASH_CANDIDATE);
    }
}
