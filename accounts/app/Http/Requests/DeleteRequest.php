<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class DeleteRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'question_id' => 'required',
            'reason' => 'required_if:question_id, 7'
        ];
    }
    public function messages()
    {
        return [
            'question_id.required' => 'Please choose a reason',
            'reason.required_if' => 'Please input your reason'
        ];
    }
}
