<?php

namespace App\Events;

use Illuminate\Auth\Events\Login;
use Illuminate\Auth\Events\Logout;
use Illuminate\Auth\Events\Registered;
use Illuminate\Queue\SerializesModels;
use TheIconic\Tracking\GoogleAnalytics\Analytics;

/**
 * @see \Illuminate\Auth\Events\Registered
 */
class AuthEventWithAnalytics
{
    use SerializesModels;

    /**
     * The authenticated user.
     *
     * @var \Illuminate\Contracts\Auth\Authenticatable
     */
    public $user;

    /** @var Registered|Login|Logout */
    protected $authEvent;

    /**
     * Create a new event instance.
     *
     * @param  Registered|Login|Logout $event
     * @return void
     */
    public function __construct($event)
    {
        $this->authEvent = $event;
        $this->user = $event->user;
    }

    public function withAnalytics(Analytics $analytics)
    {

        $analytics->setEventCategory('Auth');

        if ($this->authEvent instanceof Registered) {
            $analytics->setEventAction('sign_up');
        } elseif ($this->authEvent instanceof Login) {
            $analytics->setEventAction('login');
        } elseif ($this->authEvent instanceof Logout) {
            $analytics->setEventAction('logout');
        } else {
            $analytics->setEventAction(class_basename($this->authEvent));
        }

        // logger(__CLASS__, [$analytics->getUrl()]);
    }



}
