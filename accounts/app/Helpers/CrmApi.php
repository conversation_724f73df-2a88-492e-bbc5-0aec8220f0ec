<?php

namespace App\Helpers;

use GuzzleHttp\Client;

class CrmApi
{
    /**
     * Make request to CRM system via api
     * @param string $uri uri for crm api
     * @param array $options options that follows GuzzleHttp\Client
     * @param string $method http method for request, default is GET
     * @return \Psr\Http\Message\ResponseInterface
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public static function request($uri, $options, $method = 'GET')
    {
        $client = new Client([
            'base_uri' => config('app.crm.base_url'),
            'connect_timeout' => config('app.crm.timeout'),
            // Should verify SSL if production
            'verify' => config('app.env') === 'production',
            'headers' => [
                'api-auth-code' => config('app.crm.auth_code')
            ]
        ]);

        // prepend api/v3/
        $uri = '/api/v3/' . ltrim($uri, "/");

        return $client->request($method, $uri, $options);
    }
    /**
     * API send notification for CA
     *
     * @param int $companyId
     */
    public static function notifyEmployerLoginToCa($companyId = null, $email = null)
    {
        $options = [
            'form_params' => [
                'company_id' => $companyId,
                'email' => $email
            ]
        ];

        self::request('clients/send_notification_ca', $options, 'POST');

    }

}