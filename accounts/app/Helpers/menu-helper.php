<?php

namespace App\Helpers;

class Menu
{
    public static function Menu()
    {
        $menu = array(
            'jobs'  	=> ['title'=>	trans('home.it_jobs'),		'url'=> '//topdev.vn/it-jobs'],
            'events' 	=> ['title'=>	trans('home.tech_event'),		'url'=>'//meetup.vn'],
            'events_sub'=> [
                ['title'=>'Vietnam Mobile Day',   'url'=>'//mobileday.vn' , 'class' => 'icon-md'],
                ['title'=>'Vietnam Web Summit',   'url'=>'//vietnamwebsummit.com', 'class' => 'icon-vnw'],
                ['title'=>'TopDev Techtalk',      'url'=>'//www.facebook.com/topdevvietnam/events', 'class' => 'icon-techtalk'],
            ],
            'blogs'     => ['title'=>	trans('home.tech_blog'),		'url'=>'//topdev.vn/blog'],
            'blogs_hr'	=> ['title'=> 	trans('home.hr_blog'), 		'url'=>'//topdev.vn/blog/category/hr/'],
            'cv'        => ['title'=>	trans('home.create_cv'),		'url'=>'//topdev.vn/tao-cv-online'],
            'companies' => ['title'=>	trans('home.it_companies'),	'url'=> 'https://topdev.vn/companies'],
            'hackerrank'=> ['title'=> 	'TopDev x HackerRank', 			'url'=>'//topdev.vn/hackerrank'],
            'personality_test' => [
                'title'=> 	trans('home.personality_test'),
                'url'=>'//topdev.vn/page/trac-nghiem-tinh-cach'
            ]
        );
        return $menu;
    }

    public static function about()
    {
        $about = array(
            ['title'=>trans('footer.about_us'),						'url'=>'https://topdev.vn/about-us'],
            ['title'=>trans('footer.contact_us'),					'url'=>'https://topdev.vn/contact'],
            ['title'=>trans('footer.terms_of_service'),				'url'=>'https://topdev.vn/term-of-services'],
            ['title'=>trans('footer.career_at_topdev'),				'url'=>'https://topdev.vn/nha-tuyen-dung/topdev-buddies'],
            ['title'=>trans('footer.privacy_policy'),				'url'=>'https://topdev.vn/privacy-policy'],
            ['title'=>trans('footer.operation_regulation_topdev'),	'url'=>'https://topdev.vn/operation-regulation'],
            ['title'=>trans('footer.resolve_complaints'),			'url'=>'https://topdev.vn/resolve-complaints'],
        );
        return $about;
    }

    public static function jobseekers()
    {
        $jobseekers = array(
            ['title'=>trans('footer.title_salary_tool'), 'url' => frontend_url('tool/tinh-luong-gross-net')],
            ['title'=>trans('footer.make_cv'), 'url' => frontend_url('tao-cv-online')],
            ['title'=>trans('footer.browse_jobs'), 'url' => frontend_url('it-jobs')],
            ['title' => trans('footer.footer_personality_test'), 'url' => frontend_url('/page/trac-nghiem-tinh-cach')]
        );
        return $jobseekers;
    }

    public static function employers()
    {
        $employers = array(
            ['title'=>trans('footer.post_job'),			'url'=>frontend_url('recruit')],
            ['title'=>trans('footer.talent_solution'),	'url'=>frontend_url('page/products')],
            // ['title'=>trans('footer.join_us'),			'url'=>'https://partner.topdev.vn/'],
        );
        return $employers;
    }

    /**
     * Generate a url for the application.
     *
     * @param string $link
     * @param array $param
     *      string $param[utm_source] =>  Xác định nhà quảng cáo, trang web, ấn phẩm, v.v. đang gửi lưu lượng truy cập đến thuộc tính của bạn, ví dụ: google, bản tin 4, biển quảng cáo.
     *      string $param[utm_medium] =>  Phương tiện quảng cáo hoặc tiếp thị, ví dụ: cpc, biểu ngữ, bản tin email
     *      string $param[utm_term] =>  Xác định từ khóa tìm kiếm có trả tiền. Nếu bạn gắn thẻ chiến dịch từ khóa phải trả tiền theo cách thủ công, thì bạn cũng nên sử dụng utm_term để chỉ định từ khóa
     *      string $param[utm_content] =>  Được sử dụng để phân biệt nội dung tương tự hoặc các liên kết trong cùng một quảng cáo. Ví dụ: nếu có hai liên kết gọi hành động trong cùng một thông báo email, thì bạn có thể sử dụng utm_content và đặt các giá trị khác nhau cho từng liên kết để có thể biết phiên bản nào có hiệu quả hơn
     *      string $param[utm_campaign] =>  Tên chiến dịch riêng lẻ, khẩu hiệu, mã khuyến mại, v.v. cho sản phẩm
     * @return string
     */

    public static function utm_generate($link, $param = null)
    {
        $newLink = $link;

        if (strpos($newLink, '?') !== false) {
            $newLink .= '&';
        } else {
            $newLink .= '?';
        }

        if (is_array($param)) {
            if (isset($param['utm_source'])) {
                $newLink .= 'utm_source=' . $param['utm_source'];
            }

            if (isset($param['utm_medium'])) {
                $newLink .= '&utm_medium=' . $param['utm_medium'];
            }

            if (isset($param['utm_campaign'])) {
                $newLink .= '&utm_campaign=' . $param['utm_campaign'];
            }

            if (isset($param['utm_content'])) {
                $newLink .= '&utm_content=' . $param['utm_content'];
            }

            if (isset($param['utm_term'])) {
                $newLink .= '&utm_term=' . $param['utm_term'];
            }
        } else {
            $newLink .= 'ref='.$param;
        }

        return $newLink;
    }
}
