<?php

use Illuminate\Support\Facades\Cookie;

if (! function_exists('api_url')) {
    /**
     * Generate a url for the application.
     *
     * @param  string  $path
     * @param  mixed  $parameters
     * @param  bool|null  $secure
     * @return \Illuminate\Contracts\Routing\UrlGenerator|string
     */
    function api_url($path = null, $parameters = [], $secure = null)
    {
        $urlGenerator = app('config')['app.api_url'];

        if (is_null($path)) {
            return $urlGenerator;
        }

        return $urlGenerator .('/'.trim($path, '/'));
    }
}

if (! function_exists('frontend_url')) {
    /**
     * Generate a url for the application.
     *
     * @param  string  $path
     * @param  mixed  $parameters
     * @param  bool|null  $secure
     * @return \Illuminate\Contracts\Routing\UrlGenerator|string
     */
    function frontend_url($path = null, $parameters = [], $secure = null)
    {
        $urlGenerator = app('config')['app.frontend_url'];

        if (is_null($path)) {
            return $urlGenerator;
        }

        return $urlGenerator .('/'.trim($path, '/'));
    }
}

if (! function_exists('api_cv_url')) {
    /**
     * Generate a url for the application.
     *
     * @param  string  $path
     * @param  mixed  $parameters
     * @param  bool|null  $secure
     * @return \Illuminate\Contracts\Routing\UrlGenerator|string
     */
    function api_cv_url($path = null, $parameters = [], $secure = null)
    {
        $urlGenerator = app('config')['app.api_cv_url'];

        if (is_null($path)) {
            return $urlGenerator;
        }

        return $urlGenerator .('/'.trim($path, '/'));
    }
}
if (! function_exists('api_pt_url')) {
    /**
     * Generate a url for the application.
     *
     * @param  string  $path
     * @param  mixed  $parameters
     * @param  bool|null  $secure
     * @return \Illuminate\Contracts\Routing\UrlGenerator|string
     */
    function api_pt_url($path = null, $parameters = [], $secure = null)
    {
        $urlGenerator = app('config')['app.api_pt_url'];

        if (is_null($path)) {
            return $urlGenerator;
        }

        return $urlGenerator .('/'.trim($path, '/'));
    }
}
if (! function_exists('api_ams_url')) {
    /**
     * Generate a url for the application.
     *
     * @param  string  $path
     * @param  mixed  $parameters
     * @param  bool|null  $secure
     * @return \Illuminate\Contracts\Routing\UrlGenerator|string
     */
    function api_ams_url($path = null, $parameters = [], $secure = null)
    {
        $urlGenerator = app('config')['app.api_ams_url'];

        if (is_null($path)) {
            return $urlGenerator;
        }

        return $urlGenerator .('/'.trim($path, '/'));
    }
}

if (! function_exists('get_ga4_session')) {
    function get_ga4_session($gaId): array
    {
        $cookie = Cookie::get('_ga_'. str_replace('G-', '', $gaId));

        if (! $cookie) {
            return [];
        }

        // Extract session ID
        preg_match('/GS1\.1\.(\d+)/', $cookie, $matches);
        $sessionId = $matches[1] ?? null;

        // Extract session number
        preg_match('/GS1.\d.\d*.(\d*).(.*)/', $cookie, $matches);
        $sessionNumber = $matches[1] ?? null;

        return [
            'ga_session_id' => $sessionId,
            'ga_session_number' => $sessionNumber,
        ];
    }
}

if (!function_exists('unleash_is_enabled')) {
    function unleash_is_enabled(string $flag): bool
    {
        return \App\Facades\Unleash::isEnabled($flag);
    }
}
