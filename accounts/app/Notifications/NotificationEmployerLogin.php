<?php

namespace App\Notifications;

use App\Channels\GoogleChatChannel;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Notification;
use NotificationChannels\Telegram\TelegramMessage;

class NotificationEmployerLogin extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new notification instance.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Get the notification's delivery channels.
     *
     * @param  mixed  $notifiable
     * @return array
     */
    public function via($notifiable)
    {
        return [GoogleChatChannel::class];
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toTelegram($notifiable)
    {
        $companyName = $notifiable->company->display_name;
        $companyId = $notifiable->company->id;
        $email = $notifiable->email;
        $link = frontend_url('nha-tuyen-dung/' . $notifiable->company->slug . '-' . $companyId);
        $receiver = config('services.telegram-bot-api.receivers');

        return TelegramMessage::create()
            ->to($receiver)
            ->content(
                'Send employer login information'
                    . "\n\n" .
                    '📅 ' . now()
                    . "\n" .
                    '🏢 ' . $companyName
                    . ' (' . $companyId . ') vừa mới đăng nhập'
                    . "\n" .
                    '👤 By email: ' . $email
                    . "\n" .
                    'Link: ' . $link
            )
            ->options(['disable_web_page_preview' => true]);
    }

    /**
     * @param $notifiable
     * @return string
     */
    public function toGoogleChat($notifiable)
    {
        $companyName = $notifiable->company->display_name;
        $companyId = $notifiable->company->id;
        $email = $notifiable->email;
        $link = frontend_url('nha-tuyen-dung/' . $notifiable->company->slug . '-' . $companyId);

        return 'Send employer login information'
            . "\n\n" .
            '📅 ' . now()
            . "\n" .
            '🏢 ' . $companyName
            . ' (' . $companyId . ') vừa mới đăng nhập'
            . "\n" .
            '👤 By email: ' . $email
            . "\n" .
            'Link: ' . $link;
    }

}
