<?php

namespace App\Providers;

use Illuminate\Auth\Events\Authenticated;
use Illuminate\Auth\Events\Failed;
use Illuminate\Auth\Events\Lockout;
use Illuminate\Auth\Events\PasswordReset;
use Illuminate\Auth\Events\Registered;
use Illuminate\Auth\Events\Logout;
use Illuminate\Auth\Events\Login;
use Illuminate\Auth\Listeners\SendEmailVerificationNotification;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Event;

use Laravel\Passport\Events\AccessTokenCreated;
use Laravel\Passport\Events\RefreshTokenCreated;

use App\Listeners\DispatchAuthAnalyticsJob;
use App\Listeners\LogSuccessfulLogout;
use App\Listeners\LogSuccessfulLogoutWebDevice;
use App\Listeners\LogSuccessfulLoginWebDevice;
use App\Listeners\RevokeOldTokens;
use App\Listeners\PruneOldTokens;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event listener mappings for the application.
     *
     * @var array
     */
    protected $listen = [
        Registered::class => [
            SendEmailVerificationNotification::class,
            // DispatchAuthAnalyticsJob::class,
        ],

        Logout::class => [
            LogSuccessfulLogoutWebDevice::class,
            // DispatchAuthAnalyticsJob::class,
            LogSuccessfulLogout::class
        ],

        Login::class => [
            LogSuccessfulLoginWebDevice::class,
            // DispatchAuthAnalyticsJob::class,
        ],

        Authenticated::class => [
            // DispatchAuthAnalyticsJob::class,
        ],

        Failed::class => [
            // DispatchAuthAnalyticsJob::class,
        ],

        Lockout::class => [
            // DispatchAuthAnalyticsJob::class,
        ],

        PasswordReset::class => [
            // DispatchAuthAnalyticsJob::class,
        ],

        AccessTokenCreated::class => [
            RevokeOldTokens::class,
        ],
        RefreshTokenCreated::class => [
            PruneOldTokens::class,
        ]
    ];

    /**
     * Register any events for your application.
     *
     * @return void
     */
    public function boot()
    {
        parent::boot();

        //
    }
}
