<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Http\Resources\Json\JsonResource as Resource;
use Illuminate\Support\Facades\Validator;
use URL;
use League\OAuth2\Server\AuthorizationServer;
use Lara<PERSON>\Passport\Bridge\RefreshTokenRepository;
use Laravel\Passport\Passport;

use App\Grants\SocialGrant;
use App\Contracts\SocialUserResolverInterface;
use App\Services\SocialResolverManager;
use Laravel\Socialite\Contracts\Factory;
use App\Resolvers\AppleIdResolver;
use App\Resolvers\GoogleResolver;
use Illuminate\Support\Str;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        $this->app->resolving(AuthorizationServer::class, function (AuthorizationServer $server) {
            $server->enableGrantType(
                $this->makeSocialGrant(),
                Passport::tokensExpireIn()
            );
        });

        $this->app->singleton(SocialUserResolverInterface::class, SocialResolverManager::class);
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        if (app()->environment(['production']) || request()->getHttpHost() == 'beta-accounts.topdev.vn') {
            URL::forceScheme('https');
        }

        Resource::withoutWrapping();

        Validator::extend('phone', function ($attribute, $value, $parameters, $validator) {
            return preg_match('/^(09|01[2|6|8|9])+([0-9]{8})\b|\b(07[0|6|7|8|9]|03[2|3|4|5|6|7|8|9]|08[1|2|3|4|5|6|8|9])+([0-9]{7})\b|\b(02[4|8])+([0-9]{8})\b|\b(021[0|1|2|3|4|5|6|8|9]|022[0|1|2|5|6|7|8|9]|023[2-9]|025[1|2|4|5|6|7|8|9]|026[0|1|2|3|9]|027[0-7]|029[0|1|2|3|4|6|7|9])+([0-9]{7})\b/', $value) && strlen($value) >= 6 && strlen($value) <= 11; //
        });

        Validator::replacer('phone', function ($message, $attribute, $rule, $parameters) {
            return str_replace(':attribute', $attribute, trans('validation.phone'));
        });

        $this->bootSocialiteAppleDriver();
        $this->bootSocialiteGoogleDriver();
        $this->registerStringMacro();
    }

    /**
     * Create and configure a Social grant instance.
     *
     * @return SocialGrant
     */
    protected function makeSocialGrant(): SocialGrant
    {
        $grant = new SocialGrant(
            $this->app->make(SocialUserResolverInterface::class),
            $this->app->make(RefreshTokenRepository::class)
        );

        $grant->setRefreshTokenTTL(Passport::refreshTokensExpireIn());
        return $grant;
    }

    protected function bootSocialiteAppleDriver()
    {
        $socialite = $this->app->make(Factory::class);
        $socialite->extend(
            'apple',
            function ($app) use ($socialite) {
                $config = $app['config']['services.apple'];

                return $socialite
                    ->buildProvider(AppleIdResolver::class, $config);
            }
        );
    }

    protected function bootSocialiteGoogleDriver()
    {
        $socialite = $this->app->make(Factory::class);
        $socialite->extend(
            'google-token-info',
            function ($app) use ($socialite) {
                $config = $app['config']['services.google'];

                return $socialite
                    ->buildProvider(GoogleResolver::class, $config);
            }
        );
    }

    /**
     * Create macro for String to split string to array by specific character
     *
     * @return void
     */
    protected function registerStringMacro()
    {
        Str::macro('getSessionId', function ($code) {
            if (preg_match('/^\d{10}\.\d+$/', $code)) {
                return explode('.', $code);
            }

            return [];
        });
    }
}
