<?php

namespace App\Providers;

use GuzzleHttp\Client;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\ServiceProvider;

class UnleashServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     *
     * @return void
     */
    public function register()
    {
        // Register DefaultUnleash
        $this->app->singleton(\Unleash\Client\Unleash::class, function () {
            return \Unleash\Client\UnleashBuilder::create()
                ->withAppUrl(config('services.unleash.app_url'))
                ->withInstanceId(config('services.unleash.instance_id'))
                ->withGitlabEnvironment(config('app.env'))
                ->build();
        });
    }
}
