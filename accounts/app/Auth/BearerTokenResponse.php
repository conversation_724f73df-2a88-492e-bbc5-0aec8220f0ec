<?php

namespace App\Auth;

use Illuminate\Http\Response as HttpResponse;
use Modules\User\Entities\User;
use Psr\Http\Message\ResponseInterface;
use League\OAuth2\Server\Entities\AccessTokenEntityInterface;
use League\OAuth2\Server\ResponseTypes\BearerTokenResponse as BaseBearerTokenResponse;

class BearerTokenResponse extends BaseBearerTokenResponse
{
    public function generateHttpResponse(ResponseInterface $response)
    {
        if ($this->isUserFreezing($this->accessToken)) {
            $response = $response->withStatus(HttpResponse::HTTP_LOCKED)
                ->withHeader('pragma', 'no-cache')
                ->withHeader('cache-control', 'no-store')
                ->withHeader('content-type', 'application/json; charset=UTF-8');

            $responseParams = $this->getParamsUserIsFreezing($this->accessToken);
            $response->getBody()->write(json_encode($responseParams));

            return $response;
        }

        return parent::generateHttpResponse($response);
    }

    /**
     * @param AccessTokenEntityInterface $accessToken
     * @return array
     */
    protected function getParamsUserIsFreezing(AccessTokenEntityInterface $accessToken): array
    {
        return [
            'user_id' => $accessToken->getUserIdentifier(),
            'message' => 'The deletion process has started and please try to login later.'
        ];
    }

    /**
     * @param AccessTokenEntityInterface $accessToken
     * @return bool
     */
    protected function isUserFreezing(AccessTokenEntityInterface $accessToken)
    {
        return (bool)optional(
            User::find($accessToken->getUserIdentifier())
        )->isFreezing();
    }
}
